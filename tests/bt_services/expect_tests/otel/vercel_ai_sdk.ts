import { z } from "zod";
import isEqual from "lodash.isequal";
import { createOpenAI, OpenAIProvider } from "@ai-sdk/openai";
import {
  generateText,
  streamText,
  generateObject,
  streamObject,
  StreamTextResult,
  CoreTool,
} from "ai";
type OtelProtocol = "json" | "protobuf";
import { trace } from "@opentelemetry/api";
import { NodeSDK } from "@opentelemetry/sdk-node";
import {
  BatchSpanProcessor,
  SpanProcessor,
  ReadableSpan,
  Span,
} from "@opentelemetry/sdk-trace-base";
import { Context } from "@opentelemetry/api";
import { registerOTel } from "@vercel/otel";
import { OTLPTraceExporter as OTLPTraceExporterProtobuf } from "@opentelemetry/exporter-trace-otlp-proto";
import { OTLPTraceExporter as OTLPTraceExporterJSON } from "@opentelemetry/exporter-trace-otlp-http";
import { BT_PARENT } from "@braintrust/core";
import {
  PromptSpan,
  promptSpanSchema,
  promptSpanToPrompt,
} from "@braintrust/local";
import { initLogger, _internalGetGlobalState } from "braintrust";

const OPENAI_BASE_URL =
  process.env.OPENAI_BASE_URL ?? "http://127.0.0.1:8001/proxy";
const OPENAI_API_KEY = process.env.OPENAI_API_KEY!;

const BRAINTRUST_API_URL =
  process.env.BRAINTRUST_API_URL ?? "http://localhost:8000";
const BRAINTRUST_API_KEY = process.env.BRAINTRUST_API_KEY;

// https://opentelemetry.io/docs/zero-code/js/configuration/
process.env.OTEL_NODE_ENABLED_INSTRUMENTATIONS = "";

// https://nextjs.org/docs/app/guides/open-telemetry
process.env.NEXT_OTEL_FETCH_DISABLED = "1";

class FilteringSpanProcessor implements SpanProcessor {
  private readonly processor: SpanProcessor;
  private readonly filterPredicate: (span: ReadableSpan) => boolean;

  constructor(
    processor: SpanProcessor,
    filterPredicate: (span: ReadableSpan) => boolean,
  ) {
    this.processor = processor;
    this.filterPredicate = filterPredicate;
  }

  onStart(span: Span, parentContext: Context): void {
    // Forward all onStart calls to the underlying processor
    // Type assertion is needed due to interface differences between API and SDK Span types
    this.processor.onStart(span, parentContext);
  }

  onEnd(span: ReadableSpan): void {
    if (this.filterPredicate(span)) {
      this.processor.onEnd(span);
    }
  }

  shutdown(): Promise<void> {
    return this.processor.shutdown();
  }

  forceFlush(): Promise<void> {
    return this.processor.forceFlush();
  }
}

type VercelTracerConfig = {
  projectId: string;
  protocol: OtelProtocol;
};

function registerVercelTracer({
  apiUrl,
  apiKey,
  config,
}: {
  apiUrl: string;
  apiKey: string;
  config: VercelTracerConfig;
}): NodeSDK {
  const { projectId, protocol } = config;

  // Remove the current tracer provider if it exists.
  trace.disable();

  const OTLPTraceExporter =
    protocol === "json" ? OTLPTraceExporterJSON : OTLPTraceExporterProtobuf;
  const exporter = new OTLPTraceExporter({
    url: `${apiUrl}/otel/v1/traces`,
    headers: {
      Authorization: `Bearer ${apiKey}`,
      [BT_PARENT]: `project_id:${projectId}`,
    },
  });

  const batchProcessor = new BatchSpanProcessor(exporter, {
    scheduledDelayMillis: 1000,
  });

  const filteringProcessor = new FilteringSpanProcessor(
    batchProcessor,
    (span: ReadableSpan) => {
      // Filter out spans that start with "fetch GET"
      return !span.name.startsWith("fetch GET");
    },
  );

  const sdk = new NodeSDK({
    serviceName: "bt-vercel-tracer",
    spanProcessors: [filteringProcessor],
    // Disable all default instrumentations by providing an empty array
    instrumentations: [],
    // Explicitly disable auto-detection of instrumentations
    autoDetectResources: false,
  });
  console.log("Starting tracer");

  sdk.start();

  registerOTel({
    serviceName: "bt-vercel-tracer",
  });

  return sdk;
}

const topLevelSpanNames = [
  "ai.generateText",
  "ai.streamText",
  "ai.generateObject",
  "ai.streamObject",
];

const getTestName = (span: PromptSpan): string | undefined => {
  return z.string().optional().parse(span.metadata["ai.telemetry.functionId"]);
};

const getSpanName = (span: PromptSpan): string | undefined => {
  const spanAttributes = z.record(z.unknown()).parse(span.span_attributes);
  return z.string().optional().parse(spanAttributes["name"]);
};

type ExpectedSpan = {
  name: string;
  children?: ExpectedSpan[];
};

type TestCase = {
  testName: string;
  runTest: ({
    testName,
    openai,
  }: {
    testName: string;
    openai: OpenAIProvider;
  }) => Promise<void>;
  expectedRootSpan: ExpectedSpan;
  skipPromptCheck?: boolean;
};

function findMatchingSpan({
  testName,
  spans,
  parentSpanId,
  condition,
}: {
  testName: string;
  spans: PromptSpan[];
  parentSpanId?: string;
  condition: ExpectedSpan;
}): PromptSpan {
  const candidateSpans = [];
  for (const span of spans) {
    if (!parentSpanId && !span.is_root) {
      continue;
    }
    if (parentSpanId && !span.span_parents?.includes(parentSpanId)) {
      continue;
    }
    candidateSpans.push(span);
  }

  const conditionStr = JSON.stringify(condition, null, 2);

  const doesSpanMatch = (
    span: PromptSpan,
    condition: ExpectedSpan,
  ): boolean => {
    const spanName = getSpanName(span);
    if (spanName !== condition.name) {
      return false;
    }
    if (condition.children) {
      for (const childCondition of condition.children) {
        const childSpan = findMatchingSpan({
          testName,
          spans,
          parentSpanId: span.span_id,
          condition: childCondition,
        });
        if (!childSpan) {
          throw new Error(
            `No matching child span found for span ${spanName} (condition ${conditionStr})`,
          );
        }
      }
    }
    return true;
  };

  for (const span of candidateSpans) {
    if (doesSpanMatch(span, condition)) {
      return span;
    }
  }

  const candidateSpansStr = JSON.stringify(
    candidateSpans.map((span) => span.span_attributes),
    null,
    2,
  );
  throw new Error(
    `No matching span found for test case ${testName} (condition ${conditionStr}). Candidate spans: ${candidateSpansStr}`,
  );
}

async function consumeStream<T extends Record<string, CoreTool>>(
  stream: StreamTextResult<T, unknown>,
) {
  const toolReader = stream.fullStream.getReader();
  try {
    while (true) {
      const { done } = await toolReader.read();
      if (done) break;
    }
  } finally {
    toolReader.releaseLock();
  }
}

const EMOJI_BASE64 =
  "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAApgAAAKYB3X3/OAAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAAANCSURBVEiJtZZPbBtFFMZ/M7ubXdtdb1xSFyeilBapySVU8h8OoFaooFSqiihIVIpQBKci6KEg9Q6H9kovIHoCIVQJJCKE1ENFjnAgcaSGC6rEnxBwA04Tx43t2FnvDAfjkNibxgHxnWb2e/u992bee7tCa00YFsffekFY+nUzFtjW0LrvjRXrCDIAaPLlW0nHL0SsZtVoaF98mLrx3pdhOqLtYPHChahZcYYO7KvPFxvRl5XPp1sN3adWiD1ZAqD6XYK1b/dvE5IWryTt2udLFedwc1+9kLp+vbbpoDh+6TklxBeAi9TL0taeWpdmZzQDry0AcO+jQ12RyohqqoYoo8RDwJrU+qXkjWtfi8Xxt58BdQuwQs9qC/afLwCw8tnQbqYAPsgxE1S6F3EAIXux2oQFKm0ihMsOF71dHYx+f3NND68ghCu1YIoePPQN1pGRABkJ6Bus96CutRZMydTl+TvuiRW1m3n0eDl0vRPcEysqdXn+jsQPsrHMquGeXEaY4Yk4wxWcY5V/9scqOMOVUFthatyTy8QyqwZ+kDURKoMWxNKr2EeqVKcTNOajqKoBgOE28U4tdQl5p5bwCw7BWquaZSzAPlwjlithJtp3pTImSqQRrb2Z8PHGigD4RZuNX6JYj6wj7O4TFLbCO/Mn/m8R+h6rYSUb3ekokRY6f/YukArN979jcW+V/S8g0eT/N3VN3kTqWbQ428m9/8k0P/1aIhF36PccEl6EhOcAUCrXKZXXWS3XKd2vc/TRBG9O5ELC17MmWubD2nKhUKZa26Ba2+D3P+4/MNCFwg59oWVeYhkzgN/JDR8deKBoD7Y+ljEjGZ0sosXVTvbc6RHirr2reNy1OXd6pJsQ+gqjk8VWFYmHrwBzW/n+uMPFiRwHB2I7ih8ciHFxIkd/3Omk5tCDV1t+2nNu5sxxpDFNx+huNhVT3/zMDz8usXC3ddaHBj1GHj/As08fwTS7Kt1HBTmyN29vdwAw+/wbwLVOJ3uAD1wi/dUH7Qei66PfyuRj4Ik9is+hglfbkbfR3cnZm7chlUWLdwmprtCohX4HUtlOcQjLYCu+fzGJH2QRKvP3UNz8bWk1qMxjGTOMThZ3kvgLI5AzFfo379UAAAAASUVORK5CYII=";

const testCases: TestCase[] = [
  {
    testName: "generateText string",
    runTest: async ({ testName, openai }) => {
      await generateText({
        model: openai.chat("gpt-4o-mini"),
        temperature: 0.72,
        topP: 0.52,
        prompt:
          "What is the weather typically like in Seattle? Give a one sentence response.",
        experimental_telemetry: {
          isEnabled: true,
          functionId: testName,
          metadata: { foo: "bar" },
        },
        experimental_providerMetadata: {
          openai: {
            testName,
          },
        },
      });
    },
    expectedRootSpan: {
      name: "ai.generateText",
      children: [
        {
          name: "ai.generateText.doGenerate",
        },
      ],
    },
  },
  {
    testName: "generateText messages",
    runTest: async ({ testName, openai }) => {
      await generateText({
        model: openai.chat("gpt-4o-mini"),
        temperature: 0.9,
        messages: [
          {
            role: "system",
            content: "You are a helpful assistant.",
          },
          {
            role: "user",
            content: "I need some help.",
          },
          {
            role: "assistant",
            content: "What can I help you with?",
          },
          {
            role: "user",
            content: "Tell me what ingredients I need to make a simple cake.",
          },
        ],
        experimental_telemetry: {
          isEnabled: true,
          functionId: testName,
        },
      });
    },
    expectedRootSpan: {
      name: "ai.generateText",
      children: [
        {
          name: "ai.generateText.doGenerate",
        },
      ],
    },
  },
  {
    testName: "generateText multimodal",
    runTest: async ({ testName, openai }) => {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
      const userMessage: any = {
        role: "user",
        content: [
          {
            type: "text",
            text: "Tell a brief story about the provided image(s).",
          },
          {
            type: "image",
            image: EMOJI_BASE64,
          },
          {
            type: "image",
            image: new URL(
              "https://mystickermania.com/cdn/stickers/games/mario-banana-peel-512x512.png",
            ),
          },
        ],
      };
      await generateText({
        model: openai.chat("gpt-4o-mini"),
        temperature: 0.5,
        messages: [
          { role: "system", content: "Speak like a pirate. Arrrr." },
          userMessage,
        ],
        experimental_telemetry: {
          isEnabled: true,
          functionId: testName,
        },
      });
    },
    expectedRootSpan: {
      name: "ai.generateText",
      children: [
        {
          name: "ai.generateText.doGenerate",
        },
      ],
    },
  },
  {
    testName: "generateText tool call",
    runTest: async ({ testName, openai }) => {
      await generateText({
        model: openai.chat("gpt-4o-mini"),
        temperature: 0.5,
        tools: {
          weather: {
            description: "Get the weather in a location",
            parameters: z.object({
              location: z
                .string()
                .describe("The location to get the weather for"),
            }),
            execute: async ({ location }: { location: string }) => ({
              location,
              temperature: 68,
            }),
          },
          cityAttractions: {
            parameters: z.object({ city: z.string() }),
            execute: async ({ city }: { city: string }) => {
              if (city === "San Francisco") {
                return {
                  attractions: [
                    "Golden Gate Bridge",
                    "Alcatraz Island",
                    "Fisherman's Wharf",
                  ],
                };
              } else {
                return {
                  attractions: ["The club"],
                };
              }
            },
          },
        },
        prompt:
          "What is the weather in San Francisco and what attractions should I visit?",
        experimental_telemetry: {
          isEnabled: true,
          functionId: testName,
        },
      });
    },
    expectedRootSpan: {
      name: "ai.generateText",
      children: [
        {
          name: "ai.generateText.doGenerate",
        },
      ],
    },
  },
  {
    testName: "generateObject string",
    runTest: async ({ testName, openai }) => {
      await generateObject({
        model: openai.chat("gpt-4o-mini"),
        temperature: 0.5,
        schema: z.object({
          recipe: z.object({
            name: z.string(),
            ingredients: z.array(
              z.object({ name: z.string(), amount: z.string() }),
            ),
            steps: z.array(z.string()),
          }),
        }),
        prompt: "Generate a quick and easy chocolate chip cookie recipe.",
        experimental_telemetry: {
          isEnabled: true,
          functionId: testName,
        },
      });
    },
    expectedRootSpan: {
      name: "ai.generateObject",
      children: [
        {
          name: "ai.generateObject.doGenerate",
        },
      ],
    },
    // Structured outputs currently can't be reconstructed.
    skipPromptCheck: true,
  },
  {
    testName: "streamText string",
    runTest: async ({ testName, openai }) => {
      const stream = await streamText({
        model: openai.chat("gpt-4o-mini"),
        temperature: 0.72,
        topP: 0.52,
        prompt: "What is the weather typically like in Mexico City?",
        experimental_telemetry: {
          isEnabled: true,
          functionId: testName,
        },
      });
      await consumeStream(stream);
    },
    expectedRootSpan: {
      name: "ai.streamText",
      children: [
        {
          name: "ai.streamText.doStream",
        },
      ],
    },
  },
  {
    testName: "streamText messages",
    runTest: async ({ testName, openai }) => {
      const stream = await streamText({
        model: openai.chat("gpt-4o-mini"),
        temperature: 0.5,
        system: "You are a helpful assistant.",
        messages: [
          {
            role: "user",
            content: "I need some help.",
          },
          {
            role: "assistant",
            content: "What can I help you with?",
          },
          {
            role: "user",
            content: [
              {
                type: "text",
                text: "Tell me how to draw a cat.",
              },
            ],
          },
        ],
        experimental_telemetry: {
          isEnabled: true,
          functionId: testName,
        },
      });
      await consumeStream(stream);
    },
    expectedRootSpan: {
      name: "ai.streamText",
      children: [
        {
          name: "ai.streamText.doStream",
        },
      ],
    },
  },
  {
    testName: "streamText multimodal",
    runTest: async ({ testName, openai }) => {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
      const userMessage: any = {
        role: "user",
        content: [
          {
            type: "text",
            text: "Tell a brief story about the provided image(s).",
          },
          {
            type: "image",
            image: EMOJI_BASE64,
          },
          {
            type: "image",
            image: new URL(
              "https://mystickermania.com/cdn/stickers/games/mario-banana-peel-512x512.png",
            ),
          },
        ],
      };
      const stream = await streamText({
        model: openai.chat("gpt-4o-mini"),
        temperature: 0.5,
        messages: [
          { role: "system", content: "Speak like a pirate. Arrrr." },
          userMessage,
        ],
        experimental_telemetry: {
          isEnabled: true,
          functionId: testName,
        },
      });
      await consumeStream(stream);
    },
    expectedRootSpan: {
      name: "ai.streamText",
      children: [
        {
          name: "ai.streamText.doStream",
        },
      ],
    },
  },
  {
    testName: "streamText tool call",
    runTest: async ({ testName, openai }) => {
      const stream = await streamText({
        model: openai.chat("gpt-4o-mini"),
        temperature: 0.5,
        tools: {
          weather: {
            description: "Get the weather in a location",
            parameters: z.object({
              location: z
                .string()
                .describe("The location to get the weather for"),
            }),
            execute: async ({ location }: { location: string }) => ({
              location,
              temperature: 64,
            }),
          },
          cityAttractions: {
            parameters: z.object({ city: z.string() }),
          },
        },
        toolChoice: {
          type: "tool",
          toolName: "weather",
        },
        prompt:
          "What is the weather in Seattle and what attractions should I visit?",
        experimental_telemetry: {
          isEnabled: true,
          functionId: testName,
        },
      });
      await consumeStream(stream);
    },
    expectedRootSpan: {
      name: "ai.streamText",
      children: [
        {
          name: "ai.streamText.doStream",
        },
      ],
    },
  },
  {
    testName: "streamText multistep",
    runTest: async ({ testName, openai }) => {
      const systemMessage = `You are an expert AI assistant that explains your reasoning step by step.
        You approach every question scientifically.
        For each step, provide a title that describes what you're doing in that step, along with the content. Decide if you need another step or if you're ready to give the final answer.

        Follow these guidelines exactly:
        - Answer every question mathematically where possible.
        - USE AS MANY REASONING STEPS AS POSSIBLE. AT LEAST 4.
        - TRY AND DISPROVE YOUR ANSWER. Slow down.

        NOTE, YOUR FIRST ANSWER MIGHT BE WRONG. Check your work twice.

        Use the addReasoningStep function for each step of your reasoning.
        `;

      const stream = await streamText({
        model: openai("gpt-4o-mini"),
        temperature: 0.1,
        system: systemMessage,
        messages: [
          {
            role: "user",
            content: "How many 'r's are in the word 'strawberry'?",
          },
        ],
        maxSteps: 10,
        experimental_toolCallStreaming: true,
        tools: {
          addAReasoningStep: {
            description: "Add a step to the reasoning process.",
            parameters: z.object({
              title: z.string().describe("The title of the reasoning step"),
              content: z
                .string()
                .describe(
                  "The content of the reasoning step. WRITE OUT ALL OF YOUR WORK. Where relevant, prove things mathematically.",
                ),
              nextStep: z
                .enum(["continue", "finalAnswer"])
                .describe(
                  "Whether to continue with another step or provide the final answer",
                ),
            }),
            execute: async (params) => params,
          },
        },
        experimental_telemetry: {
          isEnabled: true,
          functionId: testName,
        },
      });
      await consumeStream(stream);
    },
    expectedRootSpan: {
      name: "ai.streamText",
      children: [
        {
          name: "ai.streamText.doStream",
        },
        {
          name: "addAReasoningStep",
        },
      ],
    },
  },
  {
    testName: "streamObject string",
    runTest: async ({ testName, openai }) => {
      const stream = await streamObject({
        model: openai.chat("gpt-4o-mini", { structuredOutputs: true }),
        temperature: 0.9,
        schema: z.object({
          recipe: z.object({
            name: z.string(),
            ingredients: z.array(
              z.object({ name: z.string(), amount: z.string() }),
            ),
            steps: z.array(z.string()),
          }),
        }),
        prompt: "Generate a celery salad recipe.",
        experimental_telemetry: {
          isEnabled: true,
          functionId: testName,
        },
      });
      const toolReader = stream.fullStream.getReader();
      try {
        while (true) {
          const { done } = await toolReader.read();
          if (done) break;
        }
      } finally {
        toolReader.releaseLock();
      }
    },
    expectedRootSpan: {
      name: "ai.streamObject",
      children: [
        {
          name: "ai.streamObject.doStream",
        },
      ],
    },
    // Structured outputs currently can't be reconstructed.
    skipPromptCheck: true,
  },
];

async function runTest({
  apiUrl,
  apiKey,
  protocol,
}: {
  apiUrl: string;
  apiKey: string;
  protocol: OtelProtocol;
}) {
  const projectName = `test-otel-collector-${protocol}`;
  const logger = initLogger({ projectName });
  const projectId = (await logger.project).id;

  const otelSdk = registerVercelTracer({
    apiUrl,
    apiKey,
    config: { projectId, protocol },
  });

  const capturedRequests: BodyInit[] = [];

  const openai = createOpenAI({
    baseURL: OPENAI_BASE_URL,
    apiKey: OPENAI_API_KEY,
    // Use a custom fetch handler to intercept LLM requests.
    fetch: async (url, options) => {
      if (options?.body) {
        capturedRequests.push(options.body);
      }
      return await fetch(url, options);
    },
  });

  const capturedRequestsByTestName: Record<string, BodyInit[]> = {};
  console.log(`Collected ${testCases.length} test cases.`);

  for (const tc of testCases) {
    const prevLength = capturedRequests.length;

    console.log("Running test:", tc.testName);
    await tc.runTest({ testName: tc.testName, openai });
    // NodeSDK doesn't have forceFlush, but we can get it from the TracerProvider
    const tracerProvider = otelSdk["_tracerProvider"];
    if (tracerProvider && typeof tracerProvider.forceFlush === "function") {
      await tracerProvider.forceFlush();
    }

    const newItems = capturedRequests.slice(prevLength);
    if (!newItems.length) {
      throw new Error("No new requests captured");
    }
    capturedRequestsByTestName[tc.testName] = newItems;
  }

  const headers = {
    Accept: "application/json",
    "Accept-Encoding": "gzip",
    Authorization: `Bearer ${apiKey}`,
  };
  const body: string = JSON.stringify({
    query: {
      from: {
        op: "function",
        name: { op: "ident", name: ["project_logs"] },
        args: [{ op: "literal", value: projectId }],
      },
      select: [{ op: "star" }],
      limit: 1000,
    },
    fmt: "json",
    use_columnstore: false,
  });
  const response = await fetch(`${apiUrl}/btql`, {
    method: "POST",
    headers,
    body,
  });
  if (!response.ok) {
    throw new Error(
      `Error fetching project logs for project ${projectName}: ${
        response.status
      }: ${await response.text()}`,
    );
  }
  const resp = await response.json();
  const rows = resp.data;

  const allSpans = z.array(promptSpanSchema).parse(rows);
  if (!allSpans.length) {
    throw new Error("No OTEL spans found");
  }
  // Order the spans from oldest to newest using the OTEL `start` attribute.
  allSpans.sort((a, b) => a.metrics["start"] - b.metrics["start"]);

  const spansByTestName = allSpans.reduce(
    (acc: Record<string, PromptSpan[]>, span: PromptSpan) => {
      const rootSpanId = span.root_span_id;
      if (!rootSpanId) {
        throw new Error("Missing root_span_id");
      }

      // Extraneous spans, e.g. fetch requests
      const testName = getTestName(span);
      if (!testName) {
        return acc;
      }
      if (!acc[testName]) {
        acc[testName] = [];
      }
      acc[testName].push(span);
      return acc;
    },
    {},
  );

  for (const { testName, expectedRootSpan, skipPromptCheck } of testCases) {
    const spans = spansByTestName[testName];
    if (!spans) {
      throw new Error(`No spans found for test ${testName}`);
    }
    console.log(
      `Test ${testName}: found ${spans.length} logged spans. Running span checks...`,
    );

    const rootSpan = findMatchingSpan({
      testName,
      spans,
      parentSpanId: undefined,
      condition: expectedRootSpan,
    });
    if (!rootSpan) {
      throw new Error(`No root span found for this test`);
    }

    const llmSpans = spans.filter((span) => {
      const name = getSpanName(span);
      return (
        name &&
        !topLevelSpanNames.includes(name) &&
        span.span_attributes.type === "llm"
      );
    });
    if (!llmSpans.length) {
      throw new Error("No spans of type LLM were found");
    }

    const capturedRequests = capturedRequestsByTestName[testName];
    if (llmSpans.length !== capturedRequests.length) {
      throw new Error(
        `Number of reconstructed prompts (${llmSpans.length}) did not match # of captured LLM calls (${capturedRequests.length}) for test: '${testName}'`,
      );
    }

    if (!skipPromptCheck) {
      await Promise.all(
        llmSpans.map(async (llmSpan, i) => {
          const prompt = await promptSpanToPrompt({ promptSpan: llmSpan });
          if (!prompt) {
            throw new Error(
              `Failed to parse span and convert to prompt: ${JSON.stringify(llmSpan, null, 2)}`,
            );
          }

          const capturedRequest = JSON.parse(capturedRequests[i].toString());
          // We don't populate this field (for now?) so remove it before comparing.
          delete capturedRequest["stream"];

          // Either object can contain `undefined` fields that are missing in
          // the other object, so JSON-roundtrip them before comparing.
          const capturedPrompt = JSON.parse(JSON.stringify(capturedRequest));
          const reconstructedPrompt = JSON.parse(JSON.stringify(prompt));

          if (!isEqual(capturedPrompt, reconstructedPrompt)) {
            console.log(`Test '${testName}': prompt mismatch!`);
            console.log(
              JSON.stringify(
                {
                  metadata: llmSpan.metadata,
                  capturedPrompt,
                  reconstructedPrompt,
                },
                null,
                2,
              ),
            );
            throw new Error(
              `Reconstructed prompt for '${testName}' did not match captured LLM call`,
            );
          }
        }),
      );
    }
  }

  console.log(`All ${protocol} tests passed :)`);
}

async function main() {
  if (!BRAINTRUST_API_KEY) {
    throw new Error("No API key found");
  }

  const protocols: OtelProtocol[] = ["json", "protobuf"];
  for (const protocol of protocols) {
    await runTest({
      apiUrl: BRAINTRUST_API_URL,
      apiKey: BRAINTRUST_API_KEY,
      protocol,
    });
  }
}

main().catch((error) => {
  console.error("Test failed:", error);
  process.exit(1);
});
