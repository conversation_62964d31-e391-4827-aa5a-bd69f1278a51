import dataclasses
import gzip
import json
import time
import unittest
from typing import List, Optional, Set
from uuid import uuid4

import requests
from braintrust.http_headers import BT_FOUND_EXISTING_HEADER

from tests.braintrust_app_test_base import LOCAL_API_URL, LOCAL_APP_URL, BraintrustAppTestBase, make_v1_url

EVENT_OBJECT_TYPE_OVERRIDE = dict(
    project="project_logs",
)


def make_request_url(object_type, extra_url_components=None):
    components = []
    if object_type:
        components.append(object_type)
    if extra_url_components:
        components.extend(extra_url_components)
    return make_v1_url(*components)


def strip_toplevel_nones(x):
    return {k: v for k, v in x.items() if v is not None}


@dataclasses.dataclass
class RequestInfo:
    verb: str
    object_type: str
    extra_url_components: Optional[List[str]] = None
    tags: Optional[Set[str]] = None

    def __post_init__(self):
        if self.extra_url_components is None:
            self.extra_url_components = []
        if self.tags is None:
            self.tags = set()

    def make_request_url(self):
        return make_request_url(self.object_type, self.extra_url_components)

    def is_create_request(self):
        return self.verb in ["post", "put"]

    def is_list_all_request(self):
        return self.verb == "get" and not self.extra_url_components

    def add_tag(self, tag):
        return dataclasses.replace(self, tags=self.tags.union({tag}))


def default_transform(request_info, obj):
    return obj


def make_list_all_filter_transform(filter_pred):
    def transform(request_info, obj):
        if request_info.is_list_all_request():
            obj["objects"] = [o for o in obj["objects"] if filter_pred(o)]
        return obj

    return transform


def strip_toplevel_nones_transform(request_info, obj):
    if request_info.verb == "get":
        if request_info.is_list_all_request():
            obj["objects"] = [strip_toplevel_nones(o) for o in obj["objects"]]
            return obj
        else:
            return strip_toplevel_nones(obj)
    return obj


def default_skip_request(request_info):
    return False


@dataclasses.dataclass
class ParentObjectEntry:
    object_type: str
    id_col_name: str


class RestApiCoverageTest(BraintrustAppTestBase):
    def setUp(self):
        self.maxDiff = None
        super().setUp()

        # Create an additional user in the org who does not belong to
        # 'Owners', so they don't have permissions to do anything.
        self.unprivileged_user_id, _, self.unprivileged_user_api_key = self.createUserInOrg(
            self.org_id, remove_from_org_owners=True
        )
        self.unprivileged_user_email = self.getUserEmail(self.unprivileged_user_id)

    def _expect_error_context(self, resp, user_email, org_name):
        if resp.status_code >= 400 and resp.status_code < 500:
            # Check that we have tagged the output response with metadata tags.
            self.assertIn(f"[user_email={user_email or ''}", resp.text)
            self.assertIn(f"[user_org={org_name or ''}", resp.text)
            self.assertIn(f"[timestamp=", resp.text)

    def _unprivileged_request(self, request_info, transform_output, **request_kwargs):
        request_kwargs = {**request_kwargs}
        if "headers" not in request_kwargs:
            request_kwargs["headers"] = {}
        request_kwargs["headers"] = {
            **request_kwargs["headers"],
            "Authorization": f"Bearer {self.unprivileged_user_api_key}",
        }

        # List-all requests should return an empty list instead of an error.
        resp = self.run_request(
            request_info.verb,
            request_info.make_request_url(),
            expect_error=not request_info.is_list_all_request(),
            **request_kwargs,
        )
        if request_info.is_list_all_request():
            resp_data = transform_output(request_info, resp.json())
            self.assertEqual(resp_data, dict(objects=[]), "Expected unprivileged get all request to return empty list")
        else:
            # It should be a 400-level error.
            self.assertGreaterEqual(resp.status_code, 400)
            self.assertLess(resp.status_code, 500)
            self._expect_error_context(resp, user_email=self.unprivileged_user_email, org_name=self.org_name)
        return resp

    def _expect_fail_with_injected_parameter(self, request_info, inject_into_field, **request_kwargs):
        if isinstance(request_kwargs.get(inject_into_field), dict):
            request_kwargs = {
                **request_kwargs,
                inject_into_field: {**request_kwargs[inject_into_field], "__bogus__": "yes"},
            }
            resp = self.run_request(
                request_info.verb,
                request_info.make_request_url(),
                expect_error=True,
                **request_kwargs,
            )
            self._expect_error_context(resp, user_email=None, org_name=None)

    def _checked_request(
        self,
        verb,
        object_type,
        extra_url_components=None,
        tags=None,
        transform_output=None,
        skip_request=None,
        expect_error=False,
        **request_kwargs,
    ):
        if transform_output is None:
            transform_output = default_transform
        if skip_request is None:
            skip_request = default_skip_request

        base_request_info = RequestInfo(verb, object_type, extra_url_components, tags)
        request_info = base_request_info.add_tag("inject_bogus_param")
        if not (
            skip_request(request_info)
            or object_type in ["prompt", "function"]
            or (extra_url_components and extra_url_components[-1] in ["insert", "fetch", "feedback"])
        ):
            for inject_into_field in ["params", "json"]:
                self._expect_fail_with_injected_parameter(request_info, inject_into_field, **request_kwargs)

        resp = self.run_request(
            base_request_info.verb, base_request_info.make_request_url(), expect_error=expect_error, **request_kwargs
        )
        if expect_error:
            return dict(data=resp.text, resp=resp)

        resp_data = transform_output(request_info, resp.json())

        # If there is a nonempty response from a get request, check that
        # specifying a 'gzip' encoding returns the same thing as the original.
        request_info = base_request_info.add_tag("gzip")
        if verb == "get" and resp.text and not skip_request(request_info):
            gzip_request_kwargs = {**request_kwargs}
            if "headers" not in gzip_request_kwargs:
                gzip_request_kwargs["headers"] = dict(Authorization=f"Bearer {self.org_api_key}")
            gzip_request_kwargs["headers"] = {**gzip_request_kwargs["headers"], "Accept-Encoding": "gzip"}
            # Use regular requests here so that we can avoid reading the text
            # stream.
            gzipped_resp = getattr(requests, verb)(
                request_info.make_request_url(),
                **gzip_request_kwargs,
                stream=True,
            )
            if not gzipped_resp.ok:
                raise Exception(f"{gzipped_resp.status_code} error (gzipped request): {gzipped_resp.text}")
            self.assertEqual(
                resp_data,
                transform_output(request_info, json.loads(gzip.decompress(gzipped_resp.raw.read()).decode())),
            )

        request_info = base_request_info.add_tag("unprivileged")
        if not skip_request(request_info):
            self._unprivileged_request(request_info, transform_output=transform_output, **request_kwargs)

        return dict(data=resp_data, resp=resp)

    def _crud_helper_create_parent_objects(self, is_within_project, parent_object_type, **request_kwargs):
        if is_within_project:
            parent_project = self._checked_request(
                "post", "project", json=dict(name="parent_project"), **request_kwargs
            )["data"]
        else:
            parent_project = None

        if parent_object_type:
            parent_object = self._checked_request(
                "post",
                parent_object_type,
                json=dict(name=f"parent_{parent_object_type}"),
                **request_kwargs,
            )["data"]
            parent_object_fields = dict(object_type=parent_object_type, object_id=parent_object["id"])
        else:
            parent_object = None
            parent_object_fields = dict()

        return parent_project, parent_object, parent_object_fields

    def _test_crud_helper(
        self,
        object_type,
        transform_output=None,
        skip_request=None,
        is_within_project=False,
        parent_object_type=None,
        has_events_api=False,
        create_extra_fields=None,
        create_extra_ignore_read_fields=None,
        create_put_nomodify_fields=None,
        list_extra_fields=None,
        create_omit_org_name=False,
        post_instead_of_put_unique=False,
        check_found_existing_header=False,
    ):
        if create_extra_fields is None:
            create_extra_fields = {}
        if create_put_nomodify_fields is None:
            create_put_nomodify_fields = set()
        if transform_output is None:
            transform_output = default_transform
        if skip_request is None:
            skip_request = default_skip_request

        def make_checked_request(request_info, expect_error=False, **request_kwargs):
            return self._checked_request(
                **dataclasses.asdict(request_info),
                transform_output=transform_output,
                skip_request=skip_request,
                expect_error=expect_error,
                **request_kwargs,
            )

        parent_project, parent_object, parent_object_fields = self._crud_helper_create_parent_objects(
            is_within_project=is_within_project, parent_object_type=parent_object_type
        )

        # Create an object with POST.
        object = make_checked_request(
            RequestInfo("post", object_type),
            json=dict(
                name="example",
                **(dict(project_id=parent_project["id"]) if parent_project else {}),
                **parent_object_fields,
                **create_extra_fields,
            ),
        )["data"]
        self.assertEqual(object["name"], "example")
        if parent_project:
            self.assertEqual(object["project_id"], parent_project["id"])
        if parent_object:
            self.assertEqual(object["object_type"], parent_object_type)
            self.assertEqual(object["object_id"], parent_object["id"])

        for field, value in create_extra_fields.items():
            if create_extra_ignore_read_fields and field in create_extra_ignore_read_fields:
                continue
            self.assertEqual(object[field], value)

        request_info = RequestInfo("post", object_type, tags={"empty_name"})
        if not skip_request(request_info):
            make_checked_request(
                request_info,
                expect_error=True,
                json=dict(
                    name="",
                    **(dict(project_id=parent_project["id"]) if parent_project else {}),
                    **parent_object_fields,
                    **create_extra_fields,
                ),
            )

        # Create an identically-named object with different fields. We should
        # get back the original object.
        request_info = RequestInfo("post", object_type, tags={"duplicate"})
        if not skip_request(request_info):
            object_dup_out = make_checked_request(
                request_info,
                json=dict(
                    name="example",
                    **(dict(project_id=parent_project["id"]) if parent_project else {}),
                    **parent_object_fields,
                    **{
                        k: v if k in create_put_nomodify_fields else (v + ["2"] if isinstance(v, list) else v + "2")
                        for k, v in create_extra_fields.items()
                    },
                ),
            )
            object_dup = object_dup_out["data"]
            if check_found_existing_header:
                self.assertIn(BT_FOUND_EXISTING_HEADER, object_dup_out["resp"].headers)
            self.assertEqual(object["id"], object_dup["id"])
            for field, value in create_extra_fields.items():
                if create_extra_ignore_read_fields and field in create_extra_ignore_read_fields:
                    continue
                self.assertEqual(object_dup[field], value, field)

        # Create an object with PUT. Provide an org_name to make sure that
        # parameter is accepted by all registration functions.
        object2 = make_checked_request(
            RequestInfo("post" if post_instead_of_put_unique else "put", object_type),
            json=dict(
                name="example2",
                **({} if create_omit_org_name else {"org_name": self.org_name}),
                **(dict(project_id=parent_project["id"]) if parent_project else {}),
                **parent_object_fields,
                **{
                    k: v if k in create_put_nomodify_fields else (v + ["2"] if isinstance(v, list) else v + "2")
                    for k, v in create_extra_fields.items()
                },
            ),
        )["data"]
        self.assertEqual(object2["name"], "example2")
        if parent_project:
            self.assertEqual(object2["project_id"], parent_project["id"])

        for field, value in create_extra_fields.items():
            if create_extra_ignore_read_fields and field in create_extra_ignore_read_fields:
                continue
            if field in create_put_nomodify_fields:
                self.assertEqual(object2[field], value, field)
            else:
                self.assertEqual(object2[field], value + ["2"] if isinstance(value, list) else value + "2", field)

        # Test patching the name.
        request_info = RequestInfo("patch", object_type, extra_url_components=[object["id"]])
        if not skip_request(request_info):
            old_object = object
            object = make_checked_request(
                request_info,
                json=dict(
                    name="example1",
                    **parent_object_fields,
                ),
            )["data"]
            self.assertEqual(object["id"], old_object["id"])
            self.assertEqual(object["name"], "example1")

        # Getting the object should return exactly the same object.
        resp = make_checked_request(
            RequestInfo("get", object_type, extra_url_components=[object["id"]]),
            params=parent_object_fields,
        )["data"]
        self.assertEqual(resp, object)

        # Listing all objects should give us exactly two with the same contents
        # as `object` and `object2`.
        #
        # To make sure listing all objects only returns objects in the org,
        # create a separate org with the user added and create an object of the
        # same type in that org.
        other_org_id, other_org_name = self.createOrg()
        self.addUserToOrg(self.user_id, other_org_id)
        other_org_api_key = self.createUserOrgApiKey(self.user_id, other_org_id)
        other_org_headers = {"Authorization": f"Bearer {other_org_api_key}"}
        (
            other_org_parent_project,
            other_org_parent_object,
            other_org_parent_object_fields,
        ) = self._crud_helper_create_parent_objects(
            is_within_project=is_within_project,
            parent_object_type=parent_object_type,
            headers=other_org_headers.copy(),
        )

        other_org_create_extra_fields = {**create_extra_fields}
        if "object_type" in create_extra_fields and create_extra_fields["object_type"] == "organization":
            other_org_create_extra_fields["object_id"] = other_org_id

        other_org_object = make_checked_request(
            RequestInfo("post", object_type),
            headers=other_org_headers,
            json=dict(
                name="example",
                **(dict(project_id=other_org_parent_project["id"]) if other_org_parent_project else {}),
                **other_org_parent_object_fields,
                **other_org_create_extra_fields,
            ),
        )["data"]

        list_extra_field_values = {f: create_extra_fields[f] for f in list_extra_fields or {}}

        def list_all_objects():
            resp = make_checked_request(
                RequestInfo("get", object_type),
                params=dict(**parent_object_fields, **list_extra_field_values),
            )["data"]
            return {o["id"]: o for o in resp["objects"]}

        objects_dict = {o["id"]: o for o in [object, object2]}
        self.assertEqual(objects_dict, list_all_objects())

        # Check listing filtering for project name / project id.
        if is_within_project:
            resp = make_checked_request(
                RequestInfo("get", object_type),
                params=dict(org_name=self.org_name, project_name=parent_project["name"]),
            )["data"]
            self.assertEqual(objects_dict, {o["id"]: o for o in resp["objects"]})
            resp = make_checked_request(
                RequestInfo("get", object_type),
                params=dict(org_name=self.org_name, project_id=parent_project["id"]),
            )["data"]
            self.assertEqual(objects_dict, {o["id"]: o for o in resp["objects"]})

        # Check listing filtering for object name.
        resp = make_checked_request(
            RequestInfo("get", object_type),
            params={f"{object_type}_name": object["name"], **parent_object_fields, **list_extra_field_values},
        )["data"]
        self.assertEqual(len(resp["objects"]), 1)
        self.assertEqual(resp["objects"][0], object)

        # Check listing filtering for object id (single and multiple).
        resp = make_checked_request(
            RequestInfo("get", object_type),
            params={"ids": object["id"], **parent_object_fields, **list_extra_field_values},
        )["data"]
        self.assertEqual(len(resp["objects"]), 1)
        self.assertEqual(resp["objects"][0], object)
        resp = make_checked_request(
            RequestInfo("get", object_type),
            params={"ids": [object["id"], object2["id"]], **parent_object_fields, **list_extra_field_values},
        )["data"]
        self.assertEqual(len(resp["objects"]), 2)
        self.assertEqual({o["id"]: o for o in resp["objects"]}, objects_dict)

        if has_events_api:
            event_object_type = EVENT_OBJECT_TYPE_OVERRIDE.get(object_type, object_type)
            supports_tracing = event_object_type in ["experiment", "project_logs"]

            # Insert some events. Do some merging within and across requests.
            # Make sure fetch captures it all.
            insert_request_info = RequestInfo("post", event_object_type, extra_url_components=[object["id"], "insert"])
            fetch_request_info = RequestInfo("get", event_object_type, extra_url_components=[object["id"], "fetch"])
            resp = make_checked_request(
                insert_request_info,
                json=dict(
                    events=[
                        dict(
                            id="row0",
                            input="foo0",
                            expected="bar0",
                            **(
                                dict(span_id="row0_span_id", root_span_id="row0_root_span_id")
                                if supports_tracing
                                else {}
                            ),
                        ),
                        dict(
                            input="foo1",
                            expected=None,
                            **(
                                dict(
                                    span_id="row1_span_id",
                                    root_span_id="row0_root_span_id",
                                    span_parents=["row0_span_id"],
                                )
                                if supports_tracing
                                else {}
                            ),
                        ),
                        dict(id="row0", expected="bar00", _is_merge=True),
                    ]
                ),
            )["data"]

            row_ids = resp["row_ids"]
            self.assertEqual(len(row_ids), 3)
            self.assertEqual(row_ids[0], "row0")
            self.assertEqual(row_ids[2], "row0")
            row1_id = row_ids[1]

            resp = make_checked_request(fetch_request_info)["data"]["events"]
            # Needed for pagination.
            for row in resp:
                self.assertIn("_xact_id", row)
                self.assertIn("root_span_id", row)
                self.assertIn("is_root", row)
                self.assertEqual(row["is_root"], not row.get("span_parents"))
                if supports_tracing:
                    if row["id"] == "row0":
                        self.assertEqual(row["span_id"], "row0_span_id")
                        self.assertEqual(row["root_span_id"], "row0_root_span_id")
                        self.assertIsNone(row.get("span_parents"))
                    else:
                        self.assertEqual(row["span_id"], "row1_span_id")
                        self.assertEqual(row["root_span_id"], "row0_root_span_id")
                        self.assertEqual(row["span_parents"], ["row0_span_id"])
            id_to_event = {r["id"]: dict(input=r.get("input"), expected=r.get("expected")) for r in resp}
            self.assertEqual(
                id_to_event,
                {"row0": dict(input="foo0", expected="bar00"), row1_id: dict(input="foo1", expected=None)},
            )

            make_checked_request(
                insert_request_info,
                json=dict(
                    events=[
                        dict(id=row1_id, input="foo11", _is_merge=True),
                    ]
                ),
            )
            resp = make_checked_request(fetch_request_info)["data"]["events"]
            id_to_event = {r["id"]: dict(input=r.get("input"), expected=r.get("expected")) for r in resp}
            self.assertEqual(
                id_to_event,
                {"row0": dict(input="foo0", expected="bar00"), row1_id: dict(input="foo11", expected=None)},
            )

        # Delete the objects we created.
        if parent_project:
            make_checked_request(RequestInfo("delete", "project", extra_url_components=[parent_project["id"]]))
        elif parent_object_fields:
            make_checked_request(RequestInfo("delete", parent_object_type, extra_url_components=[parent_object["id"]]))
        else:
            make_checked_request(RequestInfo("delete", object_type, extra_url_components=[object["id"]]))
            make_checked_request(RequestInfo("delete", object_type, extra_url_components=[object2["id"]]))
        self.assertEqual(list_all_objects(), {})

    def test_index(self):
        resp = requests.get(f"{LOCAL_API_URL}/v1")
        resp.raise_for_status()
        self.assertEqual(resp.text, "Hello, World!")

    def test_crud_project(self):
        self._test_crud_helper(
            object_type="project",
            has_events_api=True,
        )

    def test_crud_experiment(self):
        self._test_crud_helper(
            object_type="experiment",
            is_within_project=True,
            has_events_api=True,
            create_extra_fields={
                "description": "hello world",
                "tags": ["dude", "excellent"],
                "metadata": dict(goo="foo"),
            },
            create_put_nomodify_fields={"metadata"},
        )

    def test_allow_unset_experiment_name(self):
        project = self._checked_request("post", "project", json=dict(name="p"))["data"]
        self._checked_request("post", "experiment", json={"project_id": project["id"]})

    def test_crud_dataset(self):
        self._test_crud_helper(
            object_type="dataset",
            is_within_project=True,
            has_events_api=True,
            create_extra_fields={"description": "hello world", "metadata": dict(foo="bar")},
            create_put_nomodify_fields={"metadata"},
        )

    def test_crud_prompt(self):
        def skip_request(request_info):
            return "duplicate" in request_info.tags

        self._test_crud_helper(
            object_type="prompt",
            transform_output=strip_toplevel_nones_transform,
            skip_request=skip_request,
            is_within_project=True,
            create_extra_fields={
                "description": "hello world",
                "slug": "my_prompt",
                "function_data": {"type": "prompt"},
            },
            create_put_nomodify_fields={"function_data"},
        )

    def test_prompt_version(self):
        # Create a project to hold the prompts
        project = self._checked_request("post", "project", json=dict(name="test_project"))["data"]

        # Create initial prompt
        prompt = self._checked_request(
            "post",
            "prompt",
            json=dict(
                name="test_prompt",
                project_id=project["id"],
                slug="my_prompt",
                description="Initial version",
                function_data={"type": "prompt", "content": "Hello {{name}}"},
            ),
        )["data"]

        # Create second version with updated content
        prompt_v2 = self._checked_request(
            "post",
            "prompt",
            json=dict(
                name="test_prompt",
                project_id=project["id"],
                slug="my_prompt",
                description="Second version",
                function_data={"type": "prompt", "content": "Hi {{name}}"},
            ),
        )["data"]

        from braintrust.xact_ids import prettify_xact

        # Create third version with different content
        prompt_v3 = self._checked_request(
            "post",
            "prompt",
            json=dict(
                name="test_prompt",
                project_id=project["id"],
                slug="my_prompt",
                description="Third version",
                function_data={"type": "prompt", "content": "Greetings {{name}}"},
            ),
        )["data"]

        # Verify we can fetch specific versions
        v1 = self._checked_request(
            "get", "prompt", extra_url_components=[prompt["id"]], params={"version": prettify_xact(prompt["_xact_id"])}
        )["data"]
        self.assertEqual(v1["description"], "Initial version")

        # verify we can query by the numeric version as well
        v1n = self._checked_request(
            "get",
            "prompt",
            extra_url_components=[prompt["id"]],
            params={"version": prompt["_xact_id"]},
        )["data"]
        self.assertEqual(v1n["description"], "Initial version")

        v2 = self._checked_request(
            "get",
            "prompt",
            extra_url_components=[prompt["id"]],
            params={"version": prettify_xact(prompt_v2["_xact_id"])},
        )["data"]
        self.assertEqual(v2["description"], "Second version")

        v3 = self._checked_request(
            "get",
            "prompt",
            extra_url_components=[prompt["id"]],
            params={"version": prettify_xact(prompt_v3["_xact_id"])},
        )["data"]
        self.assertEqual(v3["description"], "Third version")

        # Verify latest version is returned by default
        latest = self._checked_request("get", "prompt", extra_url_components=[prompt["id"]])["data"]
        self.assertEqual(latest["description"], "Third version")

        # Verify error when requesting non-existent version
        self._checked_request(
            "get", "prompt", extra_url_components=[prompt["id"]], params=dict(version=999), expect_error=True
        )

    def test_create_prompt_empty_slug(self):
        project = self._checked_request("post", "project", json=dict(name="p"))["data"]
        self.run_request(
            "post",
            make_request_url("prompt"),
            json={"project_id": project["id"], "slug": "slug", "name": "name"},
        )
        self.run_request(
            "post",
            make_request_url("prompt"),
            json={"project_id": project["id"], "slug": "", "name": "name"},
            expect_error=True,
        )

    def test_crud_function(self):
        def skip_request(request_info):
            return "duplicate" in request_info.tags

        self._test_crud_helper(
            object_type="function",
            transform_output=strip_toplevel_nones_transform,
            skip_request=skip_request,
            is_within_project=True,
            create_extra_fields={
                "description": "hello world",
                "slug": "my_function",
                "function_data": {"type": "prompt"},
            },
            create_put_nomodify_fields={"function_data"},
        )

    def test_create_function_empty_slug(self):
        project = self._checked_request("post", "project", json=dict(name="p"))["data"]
        self.run_request(
            "post",
            make_request_url("function"),
            json={"project_id": project["id"], "slug": "slug", "name": "name"},
        )
        self.run_request(
            "post",
            make_request_url("function"),
            json={"project_id": project["id"], "slug": "", "name": "name"},
            expect_error=True,
        )

    def test_crud_role(self):
        self._test_crud_helper(
            object_type="role",
            transform_output=make_list_all_filter_transform(lambda x: x["org_id"] is not None),
            create_extra_fields={"description": "hello world"},
        )

    def test_crud_group(self):
        self._test_crud_helper(
            object_type="group",
            transform_output=make_list_all_filter_transform(
                lambda x: x["name"] not in ["Owners", "Engineers", "Viewers"]
            ),
            create_extra_fields={"description": "hello world"},
        )

    def test_crud_project_score(self):
        def skip_request(request_info):
            return "empty_name" in request_info.tags

        self._test_crud_helper(
            object_type="project_score",
            skip_request=skip_request,
            is_within_project=True,
            create_extra_fields={
                "description": "hello world",
                "score_type": "categorical",
                "categories": [dict(name="foo", value=0.5)],
            },
            create_put_nomodify_fields={"score_type", "categories"},
            check_found_existing_header=True,
        )

    def test_project_score_categorical_to_freeform_update(self):
        """Test updating project_score from categorical to free-form and verifying categories becomes null."""
        project = self.run_request("post", make_v1_url("project"), json=dict(name="test_project")).json()

        initial_score = self.run_request(
            "post",
            make_v1_url("project_score"),
            json={
                "project_id": project["id"],
                "name": "test_score",
                "description": "A test score",
                "score_type": "categorical",
                "categories": [
                    {"name": "excellent", "value": 1.0},
                    {"name": "good", "value": 0.7},
                    {"name": "poor", "value": 0.3},
                ],
            },
        ).json()

        self.assertEqual(initial_score["score_type"], "categorical")
        self.assertIsNotNone(initial_score["categories"])
        self.assertEqual(len(initial_score["categories"]), 3)
        self.assertEqual(initial_score["categories"][0]["name"], "excellent")
        self.assertEqual(initial_score["categories"][0]["value"], 1.0)

        # If we patch the project score to free-form without setting categories
        # to null, it should fail with a 400.
        resp = self.run_request(
            "patch",
            make_v1_url(f"project_score/{initial_score['id']}"),
            json={"score_type": "free-form"},
            expect_error=True,
        )
        self.assertEqual(resp.status_code, 400)

        # Update the project_score to free-form and set categories to null
        updated_score = self.run_request(
            "patch",
            make_v1_url(f"project_score/{initial_score['id']}"),
            json={"score_type": "free-form", "categories": None},
        ).json()

        # Verify the updated state
        self.assertEqual(updated_score["score_type"], "free-form")
        self.assertIsNone(updated_score["categories"])

    def test_crud_project_tag(self):
        def skip_request(request_info):
            return "empty_name" in request_info.tags

        self._test_crud_helper(
            object_type="project_tag",
            skip_request=skip_request,
            is_within_project=True,
            create_extra_fields={
                "description": "hello world",
                "color": "blue",
            },
            check_found_existing_header=True,
        )

    def test_crud_span_iframe(self):
        def skip_request(request_info):
            return "empty_name" in request_info.tags

        self._test_crud_helper(
            object_type="span_iframe",
            skip_request=skip_request,
            is_within_project=True,
            create_extra_fields={
                "description": "hello world",
                "url": "https://example.com/span_iframe",
                "post_message": True,
            },
            create_put_nomodify_fields={"post_message"},
            check_found_existing_header=True,
        )

    def test_crud_view(self):
        def skip_request(request_info):
            return "empty_name" in request_info.tags

        self._test_crud_helper(
            object_type="view",
            skip_request=skip_request,
            parent_object_type="project",
            create_extra_fields={
                "view_type": "prompts",
                "view_data": dict(
                    search=dict(
                        filter=None,
                        sort=["bar DESC", "baz ASC"],
                    ),
                ),
                "options": dict(
                    columnVisibility=dict(a=True, b=False),
                    columnOrder=["b", "a"],
                ),
            },
            create_put_nomodify_fields={"view_type", "view_data", "options"},
            create_omit_org_name=True,
            check_found_existing_header=True,
        )

    def test_crud_apikey(self):
        def transform_output(request_info, obj):
            if request_info.is_create_request():
                self.assertIn("key", obj)
                del obj["key"]
                return obj
            elif request_info.is_list_all_request():
                return make_list_all_filter_transform(lambda x: x["preview_name"][3:] not in self.org_api_key)(
                    request_info, obj
                )
            return obj

        def skip_request(request_info):
            return (
                "unprivileged" in request_info.tags
                or "duplicate" in request_info.tags
                or "empty_name" in request_info.tags
                or request_info.verb == "patch"
            )

        self._test_crud_helper(
            object_type="api_key",
            transform_output=transform_output,
            skip_request=skip_request,
            post_instead_of_put_unique=True,
        )

    def test_crud_ai_secret(self):
        def skip_request(request_info):
            return (
                "unprivileged" in request_info.tags and request_info.verb == "get"
            ) or "empty_name" in request_info.tags

        self._test_crud_helper(
            object_type="ai_secret",
            # Filter out the api key secret we add to each org during
            # initialization.
            transform_output=make_list_all_filter_transform(
                lambda x: x["name"] not in ["OPENAI_API_KEY", "ANTHROPIC_API_KEY"]
            ),
            skip_request=skip_request,
            create_extra_fields={"type": "hello world"},
        )

    def test_crud_env_var(self):
        def skip_request(request_info):
            return (
                ("unprivileged" in request_info.tags and request_info.verb == "get")
                or "empty_name" in request_info.tags,
            )

        self._test_crud_helper(
            object_type="env_var",
            # This breaks too much of the framework to put in the "parent object" system
            create_extra_fields={"value": "hello world", "object_type": "organization", "object_id": self.org_id},
            list_extra_fields={"object_type", "object_id"},
            create_put_nomodify_fields={"object_type", "object_id"},
            create_extra_ignore_read_fields={"value"},
            skip_request=skip_request,
            create_omit_org_name=True,
            is_within_project=False,
        )


class RestApiMiscTest(BraintrustAppTestBase):
    def setUp(self):
        super().setUp()
        self.maxDiff = None

    def _check_head_url(self, url, allow_redirects=True):
        for i in range(5):
            resp = requests.head(url)
            if resp.ok:
                return
            print(f"Failed HEAD request {i} on url {url}. Retrying")
            time.sleep(1)
        self.assertFalse(True)

    def test_experiment_ensure_new(self):
        project = self.run_request("post", make_v1_url("project"), json=dict(name="p")).json()

        e0 = self.run_request(
            "post", make_v1_url("experiment"), json=dict(project_id=project["id"], name="e0", description="who then")
        ).json()
        # Should return the same experiment.
        e0_same = self.run_request(
            "post", make_v1_url("experiment"), json=dict(project_id=project["id"], name="e0", description="why then")
        ).json()
        self.assertEqual(e0, e0_same)
        # Should return a unique experiment.
        e0_new = self.run_request(
            "post",
            make_v1_url("experiment"),
            json=dict(project_id=project["id"], name="e0", description="who then", ensure_new=True),
        ).json()
        self.assertNotEqual(e0["id"], e0_new["id"])
        self.assertNotEqual(e0["name"], e0_new["name"])
        self.assertEqual(e0["description"], e0_new["description"])

    def test_list_pagination(self):
        projects = []
        for i in range(5):
            project = self.run_request("post", make_v1_url("project"), json=dict(name=f"project{i}")).json()
            projects.append(project)

        # Results are returned from latest to earliest creation time.
        projects.reverse()

        resp = self.run_request("get", make_v1_url("project"), params=dict(org_name=self.org_name)).json()["objects"]
        self.assertEqual(resp, projects)

        # Try limit and pagination.
        resp = self.run_request(
            "get",
            make_v1_url("project"),
            params=dict(org_name=self.org_name, limit=2, starting_after=projects[1]["id"]),
        ).json()["objects"]
        self.assertEqual(resp, projects[2:4])
        resp = self.run_request(
            "get",
            make_v1_url("project"),
            params=dict(org_name=self.org_name, limit=3, ending_before=projects[2]["id"]),
        ).json()["objects"]
        self.assertEqual(resp, projects[:2])

    def test_list_pagination_with_prompts(self):
        # Create a project to hold the prompts
        project = self.run_request("post", make_v1_url("project"), json=dict(name="test_project")).json()

        prompts = []
        for i in range(5):
            prompt = self.run_request(
                "post",
                make_v1_url("prompt"),
                json=dict(
                    name=f"prompt{i}",
                    project_id=project["id"],
                    slug=f"my_prompt_{i}",
                    description="hello world",
                    function_data={"type": "prompt"},
                ),
            ).json()
            prompts.append(prompt)

        # Results are returned from latest to earliest creation time
        prompts.reverse()

        # Get all prompts and strip None values
        resp = self.run_request("get", make_v1_url("prompt"), params=dict(org_name=self.org_name)).json()["objects"]
        resp = [strip_toplevel_nones(p) for p in resp]
        self.assertEqual(resp, prompts)

        # Try limit and pagination with stripped None values
        resp = self.run_request(
            "get",
            make_v1_url("prompt"),
            params=dict(org_name=self.org_name, limit=2, starting_after=prompts[1]["id"]),
        ).json()["objects"]
        resp = [strip_toplevel_nones(p) for p in resp]
        self.assertEqual(resp, [strip_toplevel_nones(p) for p in prompts[2:4]])

        resp = self.run_request(
            "get",
            make_v1_url("prompt"),
            params=dict(org_name=self.org_name, limit=3, ending_before=prompts[2]["id"]),
        ).json()["objects"]
        resp = [strip_toplevel_nones(p) for p in resp]
        self.assertEqual(resp, [strip_toplevel_nones(p) for p in prompts[:2]])

    def test_spaces_in_names(self):
        project = self.run_request("post", make_v1_url("project"), json=dict(name="test project")).json()
        experiment = self.run_request(
            "post",
            make_v1_url("experiment"),
            json=dict(project_id=project["id"], name="test experiment", description="hello world"),
        ).json()
        self.assertEqual(experiment["name"], "test experiment")
        self.assertEqual(experiment["description"], "hello world")

        project_meta = self.run_request(
            "get", make_v1_url("project"), params=dict(project_name=project["name"])
        ).json()
        self.assertEqual(project_meta["objects"][0]["name"], project["name"])

        experiment_meta = self.run_request(
            "get", make_v1_url("experiment"), params=dict(experiment_name=experiment["name"])
        ).json()
        self.assertEqual(experiment_meta["objects"][0]["name"], experiment["name"])

        experiment_org_space = self.run_request(
            "get", make_v1_url("experiment"), params=dict(org_name="foo bar : !!! bing " + experiment["name"])
        ).json()
        self.assertEqual(experiment_org_space["objects"], [])

    def test_patch_deep_merge(self):
        project = self.run_request("post", make_v1_url("project"), json=dict(name="test_project")).json()
        experiment = self.run_request(
            "post",
            make_v1_url("experiment"),
            json=dict(project_id=project["id"], metadata=dict(foo="bar", baz=dict(a=3, b=4))),
        ).json()
        self.assertEqual(experiment["metadata"], dict(foo="bar", baz=dict(a=3, b=4)))
        patched = self.run_request(
            "patch",
            make_v1_url("experiment", experiment["id"]),
            json=dict(metadata=dict(spam="eggs", baz=dict(c=5))),
        ).json()
        self.assertEqual(patched["metadata"], dict(foo="bar", spam="eggs", baz=dict(a=3, b=4, c=5)))

    def test_merge_paths(self):
        project = self.run_request("post", make_v1_url("project"), json=dict(name="test_project")).json()
        self.run_request(
            "post",
            make_v1_url("project_logs", project["id"], "insert"),
            json=dict(
                events=[
                    dict(
                        id="foo",
                        input=dict(foo="bar"),
                        output=dict(bar="baz", spam=dict(eggs="goodbye")),
                        metadata=dict(yes="no"),
                    ),
                ]
            ),
        )
        self.run_request(
            "post",
            make_v1_url("project_logs", project["id"], "insert"),
            json=dict(
                events=[
                    dict(
                        id="foo",
                        input=dict(bar="foo"),
                        output=dict(goop="gorp", spam=dict(big="small")),
                        metadata=dict(why="because"),
                        _is_merge=True,
                        _merge_paths=[["input"], ["output", "spam"], ["metadata", "yes"]],
                    ),
                ]
            ),
        )
        rows = self.run_request("get", make_v1_url("project_logs", project["id"], "fetch")).json()["events"]
        self.assertEqual(
            {k: rows[0][k] for k in ["id", "input", "output", "metadata"]},
            dict(
                id="foo",
                input=dict(bar="foo"),
                output=dict(bar="baz", goop="gorp", spam=dict(big="small")),
                metadata=dict(yes="no", why="because"),
            ),
        )

    def test_fetch_params(self):
        project = self.run_request("post", make_v1_url("project"), json=dict(name="test_project")).json()
        dataset = self.run_request(
            "post", make_v1_url("dataset"), json=dict(project_id=project["id"], name="test_dataset")
        ).json()
        INSERT_URL = make_v1_url("dataset", dataset["id"], "insert")
        self.run_request(
            "post",
            INSERT_URL,
            json=dict(
                events=[
                    dict(
                        id="row0",
                        input=dict(key="hello"),
                        output="goodbye",
                        metadata=dict(a="b"),
                    ),
                    dict(
                        id="row1",
                        input=dict(key="hello"),
                        output="goodbye",
                        metadata=dict(b="c"),
                    ),
                ]
            ),
        )
        self.run_request(
            "post",
            INSERT_URL,
            json=dict(
                events=[
                    dict(
                        id="row2",
                        input=dict(key="what"),
                        output="goodgood",
                        metadata=dict(c="d"),
                    ),
                    dict(
                        id="row3",
                        input=dict(key="hello"),
                        output="goodbye",
                        metadata=dict(d="e"),
                    ),
                ]
            ),
        )

        # Get all the rows and order by (_xact_id, root_span_id) from largest to
        # smallest.
        def sorted_rows(rows):
            return sorted(rows, key=lambda r: (r["_xact_id"], r["root_span_id"]), reverse=True)

        FETCH_URL = make_v1_url("dataset", dataset["id"], "fetch")
        all_rows = sorted_rows(self.run_request("get", FETCH_URL).json()["events"])

        # Some of these queries are not supported by brainstore, so we will not
        # have a `_pagination_key` in the fetched rows. We will strip it out of
        # the comparison.
        all_rows_no_brainstore = strip_pagination_key(all_rows)

        # Get a snapshot of the last two rows using the `version` filter.
        fetched_rows = sorted_rows(
            strip_pagination_key(
                self.run_request("post", FETCH_URL, json=dict(version=all_rows[-1]["_xact_id"])).json()["events"]
            )
        )
        self.assertEqual(fetched_rows, all_rows_no_brainstore[2:])

        fetched_rows_orig_data = self.run_request("post", FETCH_URL, json=dict(limit=2)).json()
        fetched_rows = sorted_rows(fetched_rows_orig_data["events"])
        self.assertEqual(fetched_rows, all_rows[:2])

        # Get the next page. The explicitly-constructed pagination cursor should
        # be explicitly no-longer supported, but the opaque cursor should be
        # fine.
        self.run_request(
            "post",
            FETCH_URL,
            json=dict(
                limit=2,
                max_xact_id=all_rows[1]["_xact_id"],
                max_root_span_id=all_rows[1]["root_span_id"],
            ),
            expect_error=True,
        )

        fetched_rows_next_page_data = self.run_request(
            "post",
            FETCH_URL,
            json=dict(
                limit=2,
                cursor=fetched_rows_orig_data["cursor"],
            ),
        ).json()
        fetched_rows_next_page = sorted_rows(fetched_rows_next_page_data["events"])
        self.assertEqual(fetched_rows_next_page, all_rows[2:])

        # Fetching another page with the cursor should return an empty result
        # set with no cursor.
        fetched_rows_final_page_data = self.run_request(
            "post",
            FETCH_URL,
            json=dict(
                limit=1,
                cursor=fetched_rows_next_page_data["cursor"],
            ),
        ).json()
        self.assertEqual([], fetched_rows_final_page_data["events"])
        self.assertIsNone(fetched_rows_final_page_data.get("cursor"))

    def test_filter_within_span_returns_only_spans(self):
        project = self.run_request("post", make_v1_url("project"), json=dict(name="test_project")).json()
        INSERT_URL = make_v1_url("project_logs", project["id"], "insert")
        self.run_request(
            "post",
            INSERT_URL,
            json=dict(
                events=[
                    dict(
                        id="row2",
                        input=dict(key="goodbye"),
                    ),
                ]
            ),
        )
        self.run_request(
            "post",
            INSERT_URL,
            json=dict(
                events=[
                    dict(
                        id="row0",
                        input=dict(key="hello"),
                    ),
                    dict(
                        id="row1",
                        _parent_id="row0",
                        input=dict(key="goodbye"),
                    ),
                ]
            ),
        )

        # Sort rows by row id.
        def sorted_rows(rows):
            return sorted(rows, key=lambda r: r["id"])

        FETCH_URL = make_v1_url("project_logs", project["id"], "fetch")

        # Filter for input.key == "goodbye" with limit 1. We should fetch only the matching spans
        fetched_rows = sorted_rows(
            self.run_request(
                "post",
                FETCH_URL,
                json=dict(limit=1, filters=[dict(type="path_lookup", path=["input", "key"], value="goodbye")]),
            ).json()["events"]
        )
        self.assertEqual(len(fetched_rows), 1)

    def test_feedback(self):
        project = self.run_request("post", make_v1_url("project"), json=dict(name="test_project")).json()
        self.run_request(
            "post",
            make_v1_url("project_logs", project["id"], "insert"),
            json=dict(
                events=[
                    dict(
                        id="row0",
                        input="foo",
                        output="bar",
                        scores=dict(x=0.1),
                    ),
                ]
            ),
        )

        FETCH_URL = make_v1_url("project_logs", project["id"], "fetch")
        expected_rows = self.run_request("get", FETCH_URL).json()["events"]
        self.assertEqual(len(expected_rows), 1)
        self.assertEqual(expected_rows[0]["scores"], dict(x=0.1))

        # Update the score.
        resp_data = self.run_request(
            "post",
            make_v1_url("project_logs", project["id"], "feedback"),
            json=dict(
                feedback=[
                    dict(
                        id="row0",
                        scores=dict(x=0.2),
                        tags=["goop"],
                    ),
                ]
            ),
        ).json()
        self.assertEqual(resp_data, dict(status="success"))
        expected_rows = self.run_request("get", FETCH_URL).json()["events"]
        self.assertEqual(len(expected_rows), 1)
        self.assertEqual(expected_rows[0]["scores"], dict(x=0.2))
        self.assertEqual(expected_rows[0]["tags"], ["goop"])

    def test_cross_object_insert(self):
        project = self.run_request("post", make_v1_url("project"), json=dict(name="test_project")).json()
        experiment = self.run_request("post", make_v1_url("experiment"), json=dict(project_id=project["id"])).json()
        dataset = self.run_request(
            "post", make_v1_url("dataset"), json=dict(project_id=project["id"], name="test dataset")
        ).json()
        project2 = self.run_request("post", make_v1_url("project"), json=dict(name="test_project2")).json()

        input_data = dict(
            project_logs={
                project["id"]: dict(events=[dict(input="a")]),
                project2["id"]: dict(events=[dict(input="b")]),
            },
            experiment={
                experiment["id"]: dict(events=[dict(input="c")]),
            },
            dataset={
                dataset["id"]: dict(events=[dict(input="d")]),
            },
        )
        resp = self.run_request("post", make_v1_url("insert"), json=input_data)
        object_row_ids = resp.json()

        def get_row_id(object_type, id):
            return object_row_ids[object_type][id]["row_ids"][0]

        # Check that the fetched data reflects what we inserted.
        for object_type, items in input_data.items():
            for id in items.keys():
                events = self.run_request("get", make_v1_url(object_type, id, "fetch")).json()["events"]
                self.assertEqual(len(events), 1)
                self.assertEqual(events[0]["id"], get_row_id(object_type, id))
                self.assertEqual(events[0]["input"], input_data[object_type][id]["events"][0]["input"])

        # Now try inserting some updates and feedback.
        input_data2 = dict(
            project_logs={
                project["id"]: dict(
                    events=[dict(id=get_row_id("project_logs", project["id"]), input="w", _is_merge=True)],
                    feedback=[dict(id=get_row_id("project_logs", project["id"]), comment="why then")],
                ),
                project2["id"]: dict(
                    events=[dict(id=get_row_id("project_logs", project2["id"]), input="x", _is_merge=True)],
                    feedback=[dict(id=get_row_id("project_logs", project2["id"]), comment="why then")],
                ),
            },
            experiment={
                experiment["id"]: dict(
                    events=[dict(id=get_row_id("experiment", experiment["id"]), input="y", _is_merge=True)],
                    feedback=[dict(id=get_row_id("experiment", experiment["id"]), comment="why then")],
                ),
            },
            dataset={
                dataset["id"]: dict(
                    events=[dict(id=get_row_id("dataset", dataset["id"]), input="y", _is_merge=True)],
                    feedback=[dict(id=get_row_id("dataset", dataset["id"]), comment="why then")],
                ),
            },
        )
        resp = self.run_request("post", make_v1_url("insert"), json=input_data2)
        object_row_ids2 = resp.json()

        # We should have exactly the same row ID output.
        self.assertEqual(object_row_ids, object_row_ids2)

        # Check that the fetched data reflects what we updated.
        for object_type, items in input_data.items():
            for id in items.keys():
                events = self.run_request("get", make_v1_url(object_type, id, "fetch")).json()["events"]
                self.assertEqual(len(events), 1)
                self.assertEqual(events[0]["id"], get_row_id(object_type, id))
                self.assertEqual(events[0]["input"], input_data2[object_type][id]["events"][0]["input"])

    def test_experiment_summarize(self):
        project = self.run_request("post", make_v1_url("project"), json=dict(name="test_project")).json()
        experiment1 = self.run_request(
            "post", make_v1_url("experiment"), json=dict(project_id=project["id"], name="experiment1")
        ).json()
        experiment2 = self.run_request(
            "post", make_v1_url("experiment"), json=dict(project_id=project["id"], name="experiment2")
        ).json()

        # Populate with scores where experiment 2 is always 0.1 greater than
        # experiment 1.
        experiment1_events = []
        experiment2_events = []
        for i in range(10):
            experiment1_events.append(dict(input=i, scores=dict(foo=(i / 10))))
            experiment2_events.append(dict(input=i, scores=dict(foo=((i + 1) / 10))))
        self.run_request(
            "post",
            make_v1_url("insert"),
            json=dict(
                experiment={
                    experiment1["id"]: dict(events=experiment1_events),
                    experiment2["id"]: dict(events=experiment2_events),
                },
            ),
        )

        # Summarize experiment 2.
        resp = self.run_request(
            "get",
            make_v1_url("experiment", experiment2["id"], "summarize"),
            params=dict(summarize_scores="true"),
        )
        summary = resp.json()
        self.assertEqual(summary["project_name"], "test_project")
        self.assertEqual(summary["experiment_name"], "experiment2")
        self.assertEqual(summary["comparison_experiment_name"], "experiment1")
        self.assertAlmostEqual(
            summary["scores"]["foo"]["score"],
            sum(e["scores"]["foo"] for e in experiment2_events) / len(experiment2_events),
        )
        self.assertAlmostEqual(summary["scores"]["foo"]["diff"], 0.1)
        self.assertEqual(summary["scores"]["foo"]["improvements"], len(experiment2_events))
        self.assertEqual(summary["scores"]["foo"]["regressions"], 0)
        self._check_head_url(summary["project_url"])
        self._check_head_url(summary["experiment_url"])
        self.assertIn("metrics", summary)

        # Summarize experiment 2 against itself.
        resp = self.run_request(
            "get",
            make_v1_url("experiment", experiment2["id"], "summarize"),
            params=dict(summarize_scores="true", comparison_experiment_id=experiment2["id"]),
        )
        summary = resp.json()
        self.assertEqual(summary["project_name"], "test_project")
        self.assertEqual(summary["experiment_name"], "experiment2")
        self.assertEqual(summary["comparison_experiment_name"], "experiment2")
        self.assertAlmostEqual(
            summary["scores"]["foo"]["score"],
            sum(e["scores"]["foo"] for e in experiment2_events) / len(experiment2_events),
        )
        self.assertAlmostEqual(summary["scores"]["foo"]["diff"], 0)
        self.assertEqual(summary["scores"]["foo"]["improvements"], 0)
        self.assertEqual(summary["scores"]["foo"]["regressions"], 0)
        self._check_head_url(summary["project_url"])
        self._check_head_url(summary["experiment_url"])
        self.assertIn("metrics", summary)

        # No score summary.
        resp = self.run_request(
            "get",
            make_v1_url("experiment", experiment2["id"], "summarize"),
            params=dict(comparison_experiment_id=experiment2["id"]),
        )
        summary = resp.json()
        self.assertEqual(summary["project_name"], "test_project")
        self.assertEqual(summary["experiment_name"], "experiment2")
        self.assertNotIn("comparison_experiment_name", summary)
        self.assertNotIn("scores", summary)
        self.assertNotIn("metrics", summary)

    def test_dataset_summarize(self):
        project = self.run_request("post", make_v1_url("project"), json=dict(name="test_project")).json()
        dataset = self.run_request(
            "post", make_v1_url("dataset"), json=dict(project_id=project["id"], name="my_dataset")
        ).json()
        self.run_request(
            "post",
            make_v1_url("dataset", dataset["id"], "insert"),
            json=dict(events=[dict(input=i) for i in range(10)]),
        )

        resp = self.run_request(
            "get", make_v1_url("dataset", dataset["id"], "summarize"), params=dict(summarize_data="true")
        )
        summary = resp.json()
        self.assertEqual(summary["project_name"], "test_project")
        self.assertEqual(summary["dataset_name"], "my_dataset")
        self._check_head_url(summary["project_url"])
        self._check_head_url(summary["dataset_url"])
        self.assertEqual(summary["data_summary"], dict(total_records=10))

        # No data summary.
        resp = self.run_request("get", make_v1_url("dataset", dataset["id"], "summarize"))
        summary = resp.json()
        self.assertEqual(summary["project_name"], "test_project")
        self.assertEqual(summary["dataset_name"], "my_dataset")
        self._check_head_url(summary["project_url"])
        self._check_head_url(summary["dataset_url"])

    def test_list_prompts_org_name(self):
        project0 = self.run_request("post", make_v1_url("project"), json=dict(name="test_project")).json()
        prompt0 = self.run_request(
            "post",
            make_v1_url("prompt"),
            json=dict(name="example", project_id=project0["id"], slug="my_prompt"),
        ).json()

        org1_id, org1_name = self.createOrg()
        _, __, org1_api_key = self.createUserInOrg(org1_id)
        org1_headers = dict(Authorization=f"Bearer {org1_api_key}")
        try:
            project1 = self.run_request(
                "post", make_v1_url("project"), json=dict(name="test_project"), headers=org1_headers
            ).json()
            prompt1 = self.run_request(
                "post",
                make_v1_url("prompt"),
                json=dict(name="example", project_id=project1["id"], slug="my_prompt"),
                headers=org1_headers,
            ).json()

            # We should be able to just select one or the other prompt by
            # filtering by org name.
            objects = self.run_request("get", make_v1_url("prompt"), params=dict(org_name=self.org_name)).json()[
                "objects"
            ]
            self.assertEqual([o["id"] for o in objects], [prompt0["id"]])
            objects = self.run_request(
                "get", make_v1_url("prompt"), params=dict(org_name=org1_name), headers=org1_headers
            ).json()["objects"]
            self.assertEqual([o["id"] for o in objects], [prompt1["id"]])
        finally:
            BraintrustAppTestBase.tearDownDb(org1_id)

    def test_crud_role_membership(self):
        def member_permissions_tuples(member_permissions):
            return set((x["permission"], x["restrict_object_type"]) for x in member_permissions)

        role0 = self.run_request(
            "post",
            make_v1_url("role"),
            json=dict(
                name="example0",
                member_permissions=[
                    dict(permission="delete"),
                    dict(permission="update", restrict_object_type="experiment"),
                ],
            ),
        ).json()
        self.assertEqual(
            member_permissions_tuples(role0["member_permissions"]), {("delete", None), ("update", "experiment")}
        )
        self.assertEqual(role0["member_roles"], [])

        role1 = self.run_request(
            "post",
            make_v1_url("role"),
            json=dict(name="example1"),
        ).json()
        self.assertEqual(role1["member_permissions"], [])
        self.assertEqual(role1["member_roles"], [])

        role2 = self.run_request(
            "post",
            make_v1_url("role"),
            json=dict(name="example2"),
        ).json()
        self.assertEqual(role2["member_permissions"], [])
        self.assertEqual(role2["member_roles"], [])

        def request0():
            return self.run_request(
                "patch",
                make_v1_url("role", role0["id"]),
                json=dict(
                    add_member_permissions=[dict(permission="create"), dict(permission="read")],
                    add_member_roles=[role1["id"], role2["id"]],
                    remove_member_permissions=[
                        dict(permission="delete"),
                        dict(permission="update", restrict_object_type="experiment"),
                    ],
                ),
            ).json()

        role0 = request0()
        self.assertEqual(member_permissions_tuples(role0["member_permissions"]), {("create", None), ("read", None)})
        self.assertEqual(set(role0["member_roles"]), {role1["id"], role2["id"]})
        # Doing the same thing again should be idempotent.
        role0 = request0()
        self.assertEqual(member_permissions_tuples(role0["member_permissions"]), {("create", None), ("read", None)})
        self.assertEqual(set(role0["member_roles"]), {role1["id"], role2["id"]})

        role1 = self.run_request("delete", make_v1_url("role", role1["id"])).json()
        self.assertIsNotNone(role1.get("deleted_at"))

        role0 = self.run_request("get", make_v1_url("role", role0["id"])).json()
        self.assertEqual(member_permissions_tuples(role0["member_permissions"]), {("create", None), ("read", None)})
        self.assertEqual(role0["member_roles"], [role2["id"]])

        role0 = self.run_request(
            "patch",
            make_v1_url("role", role0["id"]),
            json=dict(remove_member_permissions=[dict(permission="read")], remove_member_roles=[role2["id"]]),
        ).json()
        self.assertEqual(member_permissions_tuples(role0["member_permissions"]), {("create", None)})
        self.assertEqual(set(role0["member_roles"]), set())

    def test_crud_group_membership(self):
        extra_user_id, _, __ = self.createUserInOrg(self.org_id)
        extra_user_id2, _, __ = self.createUserInOrg(self.org_id)

        group0 = self.run_request(
            "post",
            make_v1_url("group"),
            json=dict(name="example0"),
        ).json()
        self.assertEqual(group0["member_users"], [])
        self.assertEqual(group0["member_groups"], [])

        group1 = self.run_request(
            "post",
            make_v1_url("group"),
            json=dict(name="example1"),
        ).json()
        self.assertEqual(group1["member_users"], [])
        self.assertEqual(group1["member_groups"], [])

        group2 = self.run_request(
            "post",
            make_v1_url("group"),
            json=dict(name="example2"),
        ).json()
        self.assertEqual(group2["member_users"], [])
        self.assertEqual(group2["member_groups"], [])

        def request0():
            return self.run_request(
                "patch",
                make_v1_url("group", group0["id"]),
                json=dict(
                    add_member_users=[extra_user_id, extra_user_id2], add_member_groups=[group1["id"], group2["id"]]
                ),
            ).json()

        group0 = request0()
        self.assertEqual(set(group0["member_users"]), {extra_user_id, extra_user_id2})
        self.assertEqual(set(group0["member_groups"]), {group1["id"], group2["id"]})
        # Doing the same thing again should be idempotent.
        group0 = request0()
        self.assertEqual(set(group0["member_users"]), {extra_user_id, extra_user_id2})
        self.assertEqual(set(group0["member_groups"]), {group1["id"], group2["id"]})

        group1 = self.run_request("delete", make_v1_url("group", group1["id"])).json()
        self.assertIsNotNone(group1.get("deleted_at"))

        group0 = self.run_request("get", make_v1_url("group", group0["id"])).json()
        self.assertEqual(set(group0["member_users"]), {extra_user_id, extra_user_id2})
        self.assertEqual(group0["member_groups"], [group2["id"]])

        group0 = self.run_request(
            "patch",
            make_v1_url("group", group0["id"]),
            json=dict(remove_member_users=[extra_user_id2], remove_member_groups=[group2["id"]]),
        ).json()
        self.assertEqual(set(group0["member_users"]), {extra_user_id})
        self.assertEqual(set(group0["member_groups"]), set())

    def test_get_users(self):
        org1_id, org1_name = self.createOrg()
        _, __, org1_api_key = self.createUserInOrg(org1_id)
        org2_id, org2_name = self.createOrg()
        _, __, org2_api_key = self.createUserInOrg(org2_id)

        # Clear out any existing members of these orgs.
        with BraintrustAppTestBase.connect_app_db() as conn:
            with conn.cursor() as cursor:
                cursor.execute("delete from members where org_id in (%s, %s)", (org1_id, org2_id))

        user1_id, _, user1_api_key = self.createUserInOrg(org1_id)
        user2_id, _, user2_api_key = self.createUserInOrg(org2_id)
        user3_id, _, user3_api_key = self.createUserInOrg(org1_id)

        # Add user3 to org2 as well. Their API key should then provide access to
        # both orgs (even though it's technically scoped to org1).
        self.addUserToOrg(user3_id, org2_id)

        user1_given_name, user1_email = str(uuid4()), str(uuid4())
        user2_given_name, user2_email = str(uuid4()), str(uuid4())
        shared_family_name = str(uuid4())

        # Add some details to the users.
        with BraintrustAppTestBase.connect_app_db() as conn:
            with conn.cursor() as cursor:
                cursor.execute(
                    "update users set given_name=%s, family_name=%s, email=%s where id=%s",
                    (user1_given_name, shared_family_name, user1_email, user1_id),
                )
                cursor.execute(
                    "update users set given_name=%s, family_name=%s, email=%s where id=%s",
                    (user2_given_name, shared_family_name, user2_email, user2_id),
                )

        def get_user_id(api_key, id):
            return requests.get(f"{LOCAL_API_URL}/v1/user/{id}", headers=dict(Authorization=f"Bearer {api_key}"))

        def get_users(api_key, params=None):
            return requests.get(
                f"{LOCAL_API_URL}/v1/user", headers=dict(Authorization=f"Bearer {api_key}"), params=params
            ).json()["objects"]

        # With user3_api_key (scoped to org1), we can fetch org1 users but not org2 users directly.
        user1_obj = get_user_id(user3_api_key, user1_id).json()
        user3_obj = get_user_id(user3_api_key, user3_id).json()
        # Cannot fetch user2 directly since user3's API key is scoped to org1
        user2_resp = get_user_id(user3_api_key, user2_id)
        self.assertFalse(user2_resp.ok)

        # When listing all users with org1-scoped key, we only see org1 users
        all_users = get_users(user3_api_key)
        user_ids = [u["id"] for u in all_users]
        self.assertIn(user3_id, user_ids)
        self.assertIn(user1_id, user_ids)
        self.assertNotIn(user2_id, user_ids)  # user2 is in org2, not visible

        # Test filtering.
        self.assertEqual(get_users(user3_api_key, params=dict(given_name=user1_given_name)), [user1_obj])
        self.assertEqual(get_users(user3_api_key, params=dict(email=user1_email)), [user1_obj])
        self.assertEqual(get_users(user3_api_key, params=dict(org_name=org1_name)), [user3_obj, user1_obj])

        # For user2, we need to fetch using user2's API key since user3's key is scoped to org1
        user2_obj = get_user_id(user2_api_key, user2_id).json()
        self.assertEqual(get_users(user2_api_key, params=dict(given_name=user2_given_name)), [user2_obj])
        self.assertEqual(get_users(user2_api_key, params=dict(email=user2_email)), [user2_obj])

        # When filtering by org2 using user3's org1-scoped key, we get no results
        # because org-scoped keys are strictly limited to their organization
        org2_users = get_users(user3_api_key, params=dict(org_name=org2_name))
        self.assertEqual(org2_users, [])  # Empty because key is scoped to org1

        # To see org2 users, we need to use an org2-scoped key
        org2_users_with_org2_key = get_users(user2_api_key, params=dict(org_name=org2_name))
        org2_user_ids = [u["id"] for u in org2_users_with_org2_key]
        self.assertIn(user3_id, org2_user_ids)
        self.assertIn(user2_id, org2_user_ids)

        # Family name filter only shows users from the org the API key is scoped to
        family_users = get_users(user3_api_key, params=dict(family_name=shared_family_name))
        family_user_ids = [u["id"] for u in family_users]
        self.assertIn(user1_id, family_user_ids)
        self.assertNotIn(user2_id, family_user_ids)  # user2 not visible with org1-scoped key

        self.assertEqual(get_users(user3_api_key, params=dict(email="__xxx_unittest_email__")), [])

        # Test basic pagination.
        paginated_users = get_users(user3_api_key)
        self.assertEqual(len(paginated_users), 2)  # Should see exactly 2 users (user1 and user3) in org1

        # Test pagination with limit
        first_page = get_users(user3_api_key, params=dict(limit=1))
        self.assertEqual(len(first_page), 1)

        second_page = get_users(user3_api_key, params=dict(limit=1, starting_after=first_page[0]["id"]))
        self.assertEqual(len(second_page), 1)
        self.assertNotEqual(first_page[0]["id"], second_page[0]["id"])

        # Verify we have user3 and user1 in the paginated results
        paginated_ids = [first_page[0]["id"], second_page[0]["id"]]
        self.assertIn(user3_id, paginated_ids)
        self.assertIn(user1_id, paginated_ids)

        # With individual user API keys, we should only be able to fetch users
        # in the org.
        user1_users = get_users(user1_api_key)
        user1_user_ids = [u["id"] for u in user1_users]
        self.assertIn(user3_id, user1_user_ids)
        self.assertIn(user1_id, user1_user_ids)
        self.assertNotIn(user2_id, user1_user_ids)  # user2 is in org2, not visible with org1 key

        user2_users = get_users(user2_api_key)
        user2_user_ids = [u["id"] for u in user2_users]
        self.assertIn(user3_id, user2_user_ids)  # user3 is in both orgs
        self.assertIn(user2_id, user2_user_ids)
        self.assertNotIn(user1_id, user2_user_ids)  # user1 is in org1, not visible with org2 key

        # We cannot fetch one user with the other's api key.
        self.assertFalse(get_user_id(user1_api_key, user2_id).ok)
        self.assertFalse(get_user_id(user2_api_key, user1_id).ok)

    def test_user_get_org_isolation(self):
        """Test that user/get endpoint only returns users from the org associated with the API key."""
        # Create two organizations
        org1_id, org1_name = self.createOrg()
        org2_id, org2_name = self.createOrg()

        # Create API keys for each org
        api_user1_id, _, org1_api_key = self.createUserInOrg(org1_id)
        api_user2_id, _, org2_api_key = self.createUserInOrg(org2_id)

        # Clear existing members except for the API key users
        with BraintrustAppTestBase.connect_app_db() as conn:
            with conn.cursor() as cursor:
                cursor.execute(
                    "delete from members where org_id in (%s, %s) and user_id not in (%s, %s)",
                    (org1_id, org2_id, api_user1_id, api_user2_id),
                )

        # Create users in each org
        user1_in_org1_id, _, _ = self.createUserInOrg(org1_id)
        user2_in_org1_id, _, _ = self.createUserInOrg(org1_id)
        user3_in_org2_id, _, _ = self.createUserInOrg(org2_id)

        # Create a user that belongs to both orgs
        shared_user_id, _, _ = self.createUserInOrg(org1_id)
        self.addUserToOrg(shared_user_id, org2_id)

        # Make user/get request with org1 API key
        response = requests.get(
            f"{LOCAL_API_URL}/v1/user",
            headers={"Authorization": f"Bearer {org1_api_key}"},
        )
        self.assertEqual(response.status_code, 200)
        org1_users = response.json()

        # Extract user IDs from response
        org1_user_ids = {user["id"] for user in org1_users["objects"]}

        # Verify org1 API key only sees users from org1
        self.assertIn(api_user1_id, org1_user_ids)  # The API key user itself
        self.assertIn(user1_in_org1_id, org1_user_ids)
        self.assertIn(user2_in_org1_id, org1_user_ids)
        self.assertIn(shared_user_id, org1_user_ids)
        self.assertNotIn(user3_in_org2_id, org1_user_ids)
        self.assertNotIn(api_user2_id, org1_user_ids)  # The org2 API key user

        # Make user/get request with org2 API key
        response = requests.get(
            f"{LOCAL_API_URL}/v1/user",
            headers={"Authorization": f"Bearer {org2_api_key}"},
        )
        self.assertEqual(response.status_code, 200)
        org2_users = response.json()

        # Extract user IDs from response
        org2_user_ids = {user["id"] for user in org2_users["objects"]}

        # Verify org2 API key only sees users from org2
        self.assertIn(api_user2_id, org2_user_ids)  # The API key user itself
        self.assertIn(user3_in_org2_id, org2_user_ids)
        self.assertIn(shared_user_id, org2_user_ids)
        self.assertNotIn(user1_in_org1_id, org2_user_ids)
        self.assertNotIn(user2_in_org1_id, org2_user_ids)
        self.assertNotIn(api_user1_id, org2_user_ids)  # The org1 API key user

    def test_origin_field(self):
        # Test that the origin field is correctly returned when specified in project logs
        project = self.run_request("post", make_v1_url("project"), json=dict(name="test_project")).json()

        # Create a log with an origin field
        origin_data = {
            "object_type": "experiment",
            "object_id": str(uuid4()),
            "id": "original_row_id",
            "_xact_id": "original_xact_id",
            "created": "2023-01-01T00:00:00Z",
        }

        self.run_request(
            "post",
            make_v1_url("project_logs", project["id"], "insert"),
            json=dict(
                events=[dict(id="row_with_origin", input="test input", output="test output", origin=origin_data)]
            ),
        )

        # Fetch the log and verify the origin field
        resp = self.run_request("get", make_v1_url("project_logs", project["id"], "fetch")).json()
        events = resp["events"]
        self.assertEqual(len(events), 1)
        self.assertEqual(events[0]["id"], "row_with_origin")
        self.assertEqual(events[0]["origin"], origin_data)

    @unittest.skip("Flaky test - owner: austin")
    def test_fetch_default_limit(self):
        # Copied from api-ts/src/btql.ts.
        DEFAULT_LIMIT = 1000
        DEFAULT_PROJECT_LOGS_LIMIT = 10
        project = self.run_request("post", make_v1_url("project"), json=dict(name="test_project")).json()

        object_type_to_info = dict(
            experiment=dict(
                is_unlimited=True,
            ),
            dataset=dict(
                is_unlimited=True,
            ),
            project_logs=dict(
                override_default_limit=DEFAULT_PROJECT_LOGS_LIMIT,
            ),
        )

        for object_type, object_info in object_type_to_info.items():
            if object_type == "experiment":
                object_id = self.run_request(
                    "post", make_v1_url("experiment"), json=dict(project_id=project["id"])
                ).json()["id"]
            elif object_type == "dataset":
                object_id = self.run_request(
                    "post", make_v1_url("dataset"), json=dict(project_id=project["id"], name="my_dataset")
                ).json()["id"]
            else:
                object_id = project["id"]
            expected_limit = object_info.get("override_default_limit", DEFAULT_LIMIT)
            is_unlimited = object_info.get("is_unlimited", False)

            self.run_request(
                "post",
                make_v1_url(object_type, object_id, "insert"),
                json=dict(
                    events=[
                        dict(
                            id=f"row{i}",
                            input=dict(key=f"hello"),
                        )
                        for i in range(expected_limit + 1)
                    ]
                ),
            )

            # If we fetch without limit, we should get back all the rows if
            # unlimited, or just `expected_limit` otherwise.
            resp = self.run_request("get", make_v1_url(object_type, object_id, "fetch")).json()
            if is_unlimited:
                self.assertEqual(len(resp["events"]), expected_limit + 1)
                # Brainstore always returns a cursor, so we can't check for lack
                # of a cursor.
                # self.assertIsNone(resp.get("cursor"))
            else:
                self.assertEqual(len(resp["events"]), expected_limit)
                self.assertIsNotNone(resp.get("cursor"))

    def test_experiment_metadata_filter(self):
        # Create an experiment with nested metadata
        project = self.run_request(
            "post",
            make_v1_url("project"),
            json=dict(name="_test_metadata_filter"),
        ).json()

        experiment = self.run_request(
            "post",
            make_v1_url("experiment"),
            json=dict(
                project_id=project["id"],
                name="_test_nested_metadata",
                metadata={"foo": "bar", "hello": {"goodbye": "yes"}},
            ),
        ).json()

        # Test filtering by top-level metadata
        resp = self.run_request(
            "get",
            make_v1_url("experiment"),
            params={"metadata": json.dumps({"foo": "bar"})},
        ).json()
        self.assertEqual(len(resp["objects"]), 1)
        self.assertEqual(resp["objects"][0]["id"], experiment["id"])

        # Test filtering by nested metadata
        resp = self.run_request(
            "get",
            make_v1_url("experiment"),
            params={"metadata": json.dumps({"hello": {"goodbye": "yes"}})},
        ).json()
        self.assertEqual(len(resp["objects"]), 1)
        self.assertEqual(resp["objects"][0]["id"], experiment["id"])

        # Test filtering by both top-level and nested metadata
        resp = self.run_request(
            "get",
            make_v1_url("experiment"),
            params={"metadata": json.dumps({"foo": "bar", "hello": {"goodbye": "yes"}})},
        ).json()
        self.assertEqual(len(resp["objects"]), 1)
        self.assertEqual(resp["objects"][0]["id"], experiment["id"])

        # Test that mismatching filters don't return the experiment
        resp = self.run_request(
            "get",
            make_v1_url("experiment"),
            params={"metadata": json.dumps({"foo": "baz"})},
        ).json()
        self.assertEqual(len(resp["objects"]), 0)

        # Test that mismatching nested filters don't return the experiment
        resp = self.run_request(
            "get",
            make_v1_url("experiment"),
            params={"metadata": json.dumps({"hello": {"goodbye": "no"}})},
        ).json()
        self.assertEqual(len(resp["objects"]), 0)

        # Test that mismatching combined filters don't return the experiment
        resp = self.run_request(
            "get",
            make_v1_url("experiment"),
            params={"metadata": json.dumps({"foo": "bar", "hello": {"goodbye": "no"}})},
        ).json()
        self.assertEqual(len(resp["objects"]), 0)

        # Test that filtering with an empty dictionary doesn't return the experiment
        resp = self.run_request(
            "get",
            make_v1_url("experiment"),
            params={"metadata": json.dumps({})},
        ).json()
        self.assertEqual(len(resp["objects"]), 0)

    def test_unprivileged_get_id(self):
        project = self.run_request(
            "post",
            make_v1_url("project"),
            json=dict(name="p"),
        ).json()

        resp_data = self.run_request(
            "post",
            f"{LOCAL_APP_URL}/api/project/get_id",
            json=dict(
                id=project["id"],
            ),
        ).json()
        self.assertEqual(resp_data["id"], project["id"])

        self.unprivileged_user_id, _, self.unprivileged_user_api_key = self.createUserInOrg(
            self.org_id, remove_from_org_owners=True
        )

        resp = self.run_request(
            "post",
            f"{LOCAL_APP_URL}/api/project/get_id",
            json=dict(
                id=project["id"],
            ),
            headers=dict(Authorization=f"Bearer {self.unprivileged_user_api_key}"),
            expect_error=True,
        )
        self.assertGreaterEqual(resp.status_code, 400)
        self.assertLess(resp.status_code, 500)

    def test_explicit_org_name(self):
        project = self.run_request(
            "post",
            make_v1_url("project"),
            json=dict(name="p", org_name=self.org_name),
        ).json()
        self.assertEqual(project["org_id"], self.org_id)

        # But if we specify a badly-typed org name, we should get a 400 error.
        resp = self.run_request(
            "post",
            make_v1_url("project"),
            json=dict(name="p", org_name=123),
            expect_error=True,
        )
        self.assertGreaterEqual(resp.status_code, 400)
        self.assertLess(resp.status_code, 500)


def strip_pagination_key(rows):
    return [{k: v for k, v in r.items() if k not in ["_pagination_key"]} for r in rows]
