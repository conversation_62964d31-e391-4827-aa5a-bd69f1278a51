[{"error": null, "query": "select: to_string(true)", "result_rows": [{"to_string(true)": "true"}, {"to_string(true)": "true"}, {"to_string(true)": "true"}, {"to_string(true)": "true"}, {"to_string(true)": "true"}], "skip": false}, {"error": null, "query": "select: to_string(scores.bar)", "result_rows": [null, null, null, null, {"to_string(scores.bar)": "0.5"}], "skip": false}, {"error": null, "query": "select: to_string(1.6)", "result_rows": [{"to_string(1.6)": "1.6"}, {"to_string(1.6)": "1.6"}, {"to_string(1.6)": "1.6"}, {"to_string(1.6)": "1.6"}, {"to_string(1.6)": "1.6"}], "skip": false}, {"error": null, "query": "select: to_string(null)", "result_rows": [null, null, null, null, null], "skip": false}, {"error": null, "query": "select: to_number(\"!\")", "result_rows": [null, null, null, null, null], "skip": false}, {"error": null, "query": "select: to_number(scores.bar)", "result_rows": [null, null, null, null, {"to_number(scores.bar)": 0.5}], "skip": false}, {"error": null, "query": "select: to_number(1.6)", "result_rows": [{"to_number(1.6)": 1.6}, {"to_number(1.6)": 1.6}, {"to_number(1.6)": 1.6}, {"to_number(1.6)": 1.6}, {"to_number(1.6)": 1.6}], "skip": false}, {"error": null, "query": "select: to_number(null)", "result_rows": [null, null, null, null, null], "skip": false}, {"error": null, "query": "select: to_integer(\"hi\")", "result_rows": [null, null, null, null, null], "skip": false}, {"error": null, "query": "select: to_integer(scores.bar)", "result_rows": [null, null, null, null, {"to_integer(scores.bar)": 0}], "skip": false}, {"error": null, "query": "select: to_integer(1.6)", "result_rows": [{"to_integer(1.6)": 1}, {"to_integer(1.6)": 1}, {"to_integer(1.6)": 1}, {"to_integer(1.6)": 1}, {"to_integer(1.6)": 1}], "skip": false}, {"error": null, "query": "select: to_integer(null)", "result_rows": [null, null, null, null, null], "skip": false}, {"error": null, "query": "select: to_boolean(1)", "result_rows": [{"to_boolean(1)": true}, {"to_boolean(1)": true}, {"to_boolean(1)": true}, {"to_boolean(1)": true}, {"to_boolean(1)": true}], "skip": false}, {"error": null, "query": "select: to_boolean(scores.bar)", "result_rows": [null, null, null, null, {"to_boolean(scores.bar)": true}], "skip": false}, {"error": null, "query": "select: to_boolean(1.6)", "result_rows": [{"to_boolean(1.6)": true}, {"to_boolean(1.6)": true}, {"to_boolean(1.6)": true}, {"to_boolean(1.6)": true}, {"to_boolean(1.6)": true}], "skip": false}, {"error": null, "query": "select: to_boolean(null)", "result_rows": [null, null, null, null, null], "skip": false}, {"error": null, "query": "select: to_date(\"2024/05/05\")", "result_rows": [null, null, null, null, null], "skip": false}, {"error": null, "query": "select: to_date(scores.bar)", "result_rows": [null, null, null, null, null], "skip": false}, {"error": null, "query": "select: to_date(1.6)", "result_rows": [null, null, null, null, null], "skip": false}, {"error": null, "query": "select: to_date(\"2024-06-01T12:34:56Z\")", "result_rows": [null, null, null, null, null], "skip": false}, {"error": null, "query": "select: to_datetime(\"May 05, 2025\")", "result_rows": [null, null, null, null, null], "skip": false}, {"error": null, "query": "select: to_datetime(scores.bar)", "result_rows": [null, null, null, null, {"to_datetime(scores.bar)": "1970-01-01T00:00:00.5Z"}], "skip": false}, {"error": null, "query": "select: to_datetime(\"2024-06-01T12:34:56Z\")", "result_rows": [{"to_datetime(\"2024-06-01T12:34:56Z\")": "2024-06-01T12:34:56Z"}, {"to_datetime(\"2024-06-01T12:34:56Z\")": "2024-06-01T12:34:56Z"}, {"to_datetime(\"2024-06-01T12:34:56Z\")": "2024-06-01T12:34:56Z"}, {"to_datetime(\"2024-06-01T12:34:56Z\")": "2024-06-01T12:34:56Z"}, {"to_datetime(\"2024-06-01T12:34:56Z\")": "2024-06-01T12:34:56Z"}], "skip": false}, {"error": null, "query": "select: to_datetime(null)", "result_rows": [null, null, null, null, null], "skip": false}, {"error": null, "query": "select: coalesce(metadata.nullable, 'bar')", "result_rows": [{"coalesce(metadata.nullable, 'bar')": "bar"}, {"coalesce(metadata.nullable, 'bar')": "bar"}, {"coalesce(metadata.nullable, 'bar')": "bar"}, {"coalesce(metadata.nullable, 'bar')": false}, {"coalesce(metadata.nullable, 'bar')": true}], "skip": false}, {"error": null, "query": "select: coalesce(metadata.nullable, metadata.split)", "result_rows": [null, {"coalesce(metadata.nullable, metadata.split)": "test"}, {"coalesce(metadata.nullable, metadata.split)": "test"}, {"coalesce(metadata.nullable, metadata.split)": false}, {"coalesce(metadata.nullable, metadata.split)": true}], "skip": false}, {"error": null, "query": "select: coalesce(metadata.nullable, scores.foo)", "result_rows": [null, null, {"coalesce(metadata.nullable, scores.foo)": 0.25}, {"coalesce(metadata.nullable, scores.foo)": false}, {"coalesce(metadata.nullable, scores.foo)": true}], "skip": false}, {"error": null, "query": "select: coalesce(null, 1)", "result_rows": [{"coalesce(null, 1)": 1}, {"coalesce(null, 1)": 1}, {"coalesce(null, 1)": 1}, {"coalesce(null, 1)": 1}, {"coalesce(null, 1)": 1}], "skip": false}, {"error": null, "query": "select: coalesce(1, 2)", "result_rows": [{"coalesce(1, 2)": 1}, {"coalesce(1, 2)": 1}, {"coalesce(1, 2)": 1}, {"coalesce(1, 2)": 1}, {"coalesce(1, 2)": 1}], "skip": false}, {"error": null, "query": "select: coalesce(null, null)", "result_rows": [null, null, null, null, null], "skip": false}, {"error": null, "query": "select: coalesce(1.5, 2.5)", "result_rows": [{"coalesce(1.5, 2.5)": 1.5}, {"coalesce(1.5, 2.5)": 1.5}, {"coalesce(1.5, 2.5)": 1.5}, {"coalesce(1.5, 2.5)": 1.5}, {"coalesce(1.5, 2.5)": 1.5}], "skip": false}, {"error": null, "query": "select: coalesce(scores.bar, 0.5)", "result_rows": [{"coalesce(scores.bar, 0.5)": 0.5}, {"coalesce(scores.bar, 0.5)": 0.5}, {"coalesce(scores.bar, 0.5)": 0.5}, {"coalesce(scores.bar, 0.5)": 0.5}, {"coalesce(scores.bar, 0.5)": 0.5}], "skip": false}, {"error": null, "query": "select: coalesce(counts.foo, -1)", "result_rows": [{"coalesce(counts.foo, -1)": -1}, {"coalesce(counts.foo, -1)": -1}, {"coalesce(counts.foo, -1)": -7}, {"coalesce(counts.foo, -1)": 10}, {"coalesce(counts.foo, -1)": 3}], "skip": false}, {"error": null, "query": "select: coalesce(metadata.nullable, false)", "result_rows": [{"coalesce(metadata.nullable, false)": false}, {"coalesce(metadata.nullable, false)": false}, {"coalesce(metadata.nullable, false)": false}, {"coalesce(metadata.nullable, false)": false}, {"coalesce(metadata.nullable, false)": true}], "skip": false}, {"error": null, "query": "select: coalesce(scores.bar, scores.foo)", "result_rows": [null, null, {"coalesce(scores.bar, scores.foo)": 0.25}, {"coalesce(scores.bar, scores.foo)": 0.5}, {"coalesce(scores.bar, scores.foo)": 1}], "skip": false}, {"error": null, "query": "select: coalesce(counts.bar, counts.foo)", "result_rows": [null, null, {"coalesce(counts.bar, counts.foo)": 0}, {"coalesce(counts.bar, counts.foo)": 10}, {"coalesce(counts.bar, counts.foo)": 2}], "skip": false}, {"error": null, "query": "select: coalesce(scores.bar > 0.5, true)", "result_rows": [{"coalesce(scores.bar > 0.5, true)": false}, {"coalesce(scores.bar > 0.5, true)": true}, {"coalesce(scores.bar > 0.5, true)": true}, {"coalesce(scores.bar > 0.5, true)": true}, {"coalesce(scores.bar > 0.5, true)": true}], "skip": false}, {"error": null, "query": "select: coalesce(scores.bar > 0.5, false)", "result_rows": [{"coalesce(scores.bar > 0.5, false)": false}, {"coalesce(scores.bar > 0.5, false)": false}, {"coalesce(scores.bar > 0.5, false)": false}, {"coalesce(scores.bar > 0.5, false)": false}, {"coalesce(scores.bar > 0.5, false)": false}], "skip": false}, {"error": null, "query": "select: coalesce()", "result_rows": [null, null, null, null, null], "skip": false}, {"error": null, "query": "select: coalesce(null)", "result_rows": [null, null, null, null, null], "skip": false}, {"error": null, "query": "select: coalesce(1)", "result_rows": [{"coalesce(1)": 1}, {"coalesce(1)": 1}, {"coalesce(1)": 1}, {"coalesce(1)": 1}, {"coalesce(1)": 1}], "skip": false}, {"error": null, "query": "select: coalesce('test')", "result_rows": [{"coalesce('test')": "test"}, {"coalesce('test')": "test"}, {"coalesce('test')": "test"}, {"coalesce('test')": "test"}, {"coalesce('test')": "test"}], "skip": false}, {"error": null, "query": "select: coalesce(true)", "result_rows": [{"coalesce(true)": true}, {"coalesce(true)": true}, {"coalesce(true)": true}, {"coalesce(true)": true}, {"coalesce(true)": true}], "skip": false}, {"error": null, "query": "select: coalesce(scores.foo)", "result_rows": [null, null, {"coalesce(scores.foo)": 0.25}, {"coalesce(scores.foo)": 0}, {"coalesce(scores.foo)": 1}], "skip": false}, {"error": null, "query": "select: coalesce(metadata.nullable)", "result_rows": [null, null, null, {"coalesce(metadata.nullable)": false}, {"coalesce(metadata.nullable)": true}], "skip": false}, {"error": null, "query": "select: coalesce(null, null, 1)", "result_rows": [{"coalesce(null, null, 1)": 1}, {"coalesce(null, null, 1)": 1}, {"coalesce(null, null, 1)": 1}, {"coalesce(null, null, 1)": 1}, {"coalesce(null, null, 1)": 1}], "skip": false}, {"error": null, "query": "select: coalesce(null, 2, 3)", "result_rows": [{"coalesce(null, 2, 3)": 2}, {"coalesce(null, 2, 3)": 2}, {"coalesce(null, 2, 3)": 2}, {"coalesce(null, 2, 3)": 2}, {"coalesce(null, 2, 3)": 2}], "skip": false}, {"error": null, "query": "select: coalesce(1, 2, 3)", "result_rows": [{"coalesce(1, 2, 3)": 1}, {"coalesce(1, 2, 3)": 1}, {"coalesce(1, 2, 3)": 1}, {"coalesce(1, 2, 3)": 1}, {"coalesce(1, 2, 3)": 1}], "skip": false}, {"error": null, "query": "select: coalesce(null, null, null)", "result_rows": [null, null, null, null, null], "skip": false}, {"error": null, "query": "select: coalesce(null, null, 'default')", "result_rows": [{"coalesce(null, null, 'default')": "default"}, {"coalesce(null, null, 'default')": "default"}, {"coalesce(null, null, 'default')": "default"}, {"coalesce(null, null, 'default')": "default"}, {"coalesce(null, null, 'default')": "default"}], "skip": false}, {"error": null, "query": "select: coalesce(metadata.nullable, scores.foo, 'fallback')", "result_rows": [null, null, {"coalesce(metadata.nullable, scores.foo, 'fallback')": 0.25}, {"coalesce(metadata.nullable, scores.foo, 'fallback')": false}, {"coalesce(metadata.nullable, scores.foo, 'fallback')": true}], "skip": false}, {"error": null, "query": "select: coalesce(null, metadata.split, scores.bar, 0.5)", "result_rows": [{"coalesce(null, metadata.split, scores.bar, 0.5)": "test"}, {"coalesce(null, metadata.split, scores.bar, 0.5)": "test"}, {"coalesce(null, metadata.split, scores.bar, 0.5)": "train"}, {"coalesce(null, metadata.split, scores.bar, 0.5)": "train"}, {"coalesce(null, metadata.split, scores.bar, 0.5)": 0.5}], "skip": false}, {"error": null, "query": "select: coalesce(scores.bar, counts.foo, metadata.nullable, 'final')", "result_rows": [null, null, {"coalesce(scores.bar, counts.foo, metadata.nullable, 'final')": -7}, {"coalesce(scores.bar, counts.foo, metadata.nullable, 'final')": 0.5}, {"coalesce(scores.bar, counts.foo, metadata.nullable, 'final')": 10}], "skip": false}, {"error": null, "query": "select: coalesce(null, null, null, 42)", "result_rows": [{"coalesce(null, null, null, 42)": 42}, {"coalesce(null, null, null, 42)": 42}, {"coalesce(null, null, null, 42)": 42}, {"coalesce(null, null, null, 42)": 42}, {"coalesce(null, null, null, 42)": 42}], "skip": false}, {"error": null, "query": "select: coalesce(null, null, null, null)", "result_rows": [null, null, null, null, null], "skip": false}, {"error": null, "query": "select: coalesce(null, scores.foo, null, counts.bar, 'default')", "result_rows": [null, null, {"coalesce(null, scores.foo, null, counts.bar, 'default')": 0.25}, {"coalesce(null, scores.foo, null, counts.bar, 'default')": 0}, {"coalesce(null, scores.foo, null, counts.bar, 'default')": 1}], "skip": false}, {"error": null, "query": "select: coalesce(metadata.nullable, null, scores.bar, null, counts.foo, 'last')", "result_rows": [null, null, {"coalesce(metadata.nullable, null, scores.bar, null, counts.foo, 'last')": -7}, {"coalesce(metadata.nullable, null, scores.bar, null, counts.foo, 'last')": false}, {"coalesce(metadata.nullable, null, scores.bar, null, counts.foo, 'last')": true}], "skip": false}, {"error": null, "query": "select: coalesce(null, 1, 'string', true)", "result_rows": [{"coalesce(null, 1, 'string', true)": 1}, {"coalesce(null, 1, 'string', true)": 1}, {"coalesce(null, 1, 'string', true)": 1}, {"coalesce(null, 1, 'string', true)": 1}, {"coalesce(null, 1, 'string', true)": 1}], "skip": false}, {"error": null, "query": "select: coalesce(null, null, 1.5, 'backup', false)", "result_rows": [{"coalesce(null, null, 1.5, 'backup', false)": 1.5}, {"coalesce(null, null, 1.5, 'backup', false)": 1.5}, {"coalesce(null, null, 1.5, 'backup', false)": 1.5}, {"coalesce(null, null, 1.5, 'backup', false)": 1.5}, {"coalesce(null, null, 1.5, 'backup', false)": 1.5}], "skip": false}, {"error": null, "query": "select: upper(metadata.split)", "result_rows": [null, {"upper(metadata.split)": "TEST"}, {"upper(metadata.split)": "TEST"}, {"upper(metadata.split)": "TRAIN"}, {"upper(metadata.split)": "TRAIN"}], "skip": false}, {"error": null, "query": "select: upper(null)", "result_rows": [null, null, null, null, null], "skip": false}, {"error": "btql bind failed: Error: Expected 1 arguments (received 0) for function: upper ... at line 1, column 9:\n1:  select: upper()\n            ^^^^^^^", "query": "select: upper()", "result_rows": [], "skip": false}, {"error": null, "query": "select: metadata.nullable, upper(metadata.nullable)", "result_rows": [null, null, null, {"nullable": false, "upper(metadata.nullable)": "FALSE"}, {"nullable": true, "upper(metadata.nullable)": "TRUE"}], "skip": false}, {"error": null, "query": "select: upper(1)", "result_rows": [{"upper(1)": 1}, {"upper(1)": 1}, {"upper(1)": 1}, {"upper(1)": 1}, {"upper(1)": 1}], "skip": false}, {"error": null, "query": "select: upper(true)", "result_rows": [{"upper(true)": "TRUE"}, {"upper(true)": "TRUE"}, {"upper(true)": "TRUE"}, {"upper(true)": "TRUE"}, {"upper(true)": "TRUE"}], "skip": false}, {"error": null, "query": "select: upper(month('2024-03-01T00:00:00Z' - interval 1 day))", "result_rows": [{"upper(month('2024-03-01T00:00:00Z' - interval 1 day))": "2024-02-01T00:00:00Z"}, {"upper(month('2024-03-01T00:00:00Z' - interval 1 day))": "2024-02-01T00:00:00Z"}, {"upper(month('2024-03-01T00:00:00Z' - interval 1 day))": "2024-02-01T00:00:00Z"}, {"upper(month('2024-03-01T00:00:00Z' - interval 1 day))": "2024-02-01T00:00:00Z"}, {"upper(month('2024-03-01T00:00:00Z' - interval 1 day))": "2024-02-01T00:00:00Z"}], "skip": false}, {"error": "btql bind failed: Error: Expected 1 arguments (received 2) for function: upper ... at line 1, column 9:\n1:  select: upper(metadata.split, metadata.caps)\n            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^", "query": "select: upper(metadata.split, metadata.caps)", "result_rows": [], "skip": false}, {"error": null, "query": "select: lower(metadata.caps)", "result_rows": [null, null, null, {"lower(metadata.caps)": "hello world"}, {"lower(metadata.caps)": "world hello"}], "skip": false}, {"error": null, "query": "select: lower(null)", "result_rows": [null, null, null, null, null], "skip": false}, {"error": "btql bind failed: Error: Expected 1 arguments (received 0) for function: lower ... at line 1, column 9:\n1:  select: lower()\n            ^^^^^^^", "query": "select: lower()", "result_rows": [], "skip": false}, {"error": null, "query": "select: lower(metadata.nullable)", "result_rows": [null, null, null, {"lower(metadata.nullable)": "false"}, {"lower(metadata.nullable)": "true"}], "skip": false}, {"error": null, "query": "select: lower(1)", "result_rows": [{"lower(1)": 1}, {"lower(1)": 1}, {"lower(1)": 1}, {"lower(1)": 1}, {"lower(1)": 1}], "skip": false}, {"error": null, "query": "select: lower(true)", "result_rows": [{"lower(true)": "true"}, {"lower(true)": "true"}, {"lower(true)": "true"}, {"lower(true)": "true"}, {"lower(true)": "true"}], "skip": false}, {"error": null, "query": "select: lower(month('2024-03-01T00:00:00Z' - interval 1 day))", "result_rows": [{"lower(month('2024-03-01T00:00:00Z' - interval 1 day))": "2024-02-01T00:00:00Z"}, {"lower(month('2024-03-01T00:00:00Z' - interval 1 day))": "2024-02-01T00:00:00Z"}, {"lower(month('2024-03-01T00:00:00Z' - interval 1 day))": "2024-02-01T00:00:00Z"}, {"lower(month('2024-03-01T00:00:00Z' - interval 1 day))": "2024-02-01T00:00:00Z"}, {"lower(month('2024-03-01T00:00:00Z' - interval 1 day))": "2024-02-01T00:00:00Z"}], "skip": false}, {"error": "btql bind failed: Error: Expected 1 arguments (received 2) for function: lower ... at line 1, column 9:\n1:  select: lower(metadata.split, metadata.caps)\n            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^", "query": "select: lower(metadata.split, metadata.caps)", "result_rows": [], "skip": false}, {"error": null, "query": "select: concat(metadata.split, '.', metadata.nullable, '.', metadata.caps)", "result_rows": [{"concat(metadata.split, '.', metadata.nullable, '.', metadata.caps)": ".."}, {"concat(metadata.split, '.', metadata.nullable, '.', metadata.caps)": "test.."}, {"concat(metadata.split, '.', metadata.nullable, '.', metadata.caps)": "test..WoRLD HElLO"}, {"concat(metadata.split, '.', metadata.nullable, '.', metadata.caps)": "train.false."}, {"concat(metadata.split, '.', metadata.nullable, '.', metadata.caps)": "train.true.HELLO WORLD"}], "skip": false}, {"error": "Internal error: concat function requires at least 1 argument", "query": "select: concat()", "result_rows": [], "skip": false}, {"error": null, "query": "select: concat(metadata.nullable)", "result_rows": [{"concat(metadata.nullable)": ""}, {"concat(metadata.nullable)": ""}, {"concat(metadata.nullable)": ""}, {"concat(metadata.nullable)": "false"}, {"concat(metadata.nullable)": "true"}], "skip": false}, {"error": null, "query": "select: concat(null, null)", "result_rows": [{"concat(null, null)": ""}, {"concat(null, null)": ""}, {"concat(null, null)": ""}, {"concat(null, null)": ""}, {"concat(null, null)": ""}], "skip": false}, {"error": null, "query": "select: concat(1, true, 2.5, month('2024-03-01T00:00:00Z' - interval 1 day))", "result_rows": [{"concat(1, true, 2.5, month('2024-03-01T00:00:00Z' - interval 1 day))": "12.52024-02-01T00:00:00Z"}, {"concat(1, true, 2.5, month('2024-03-01T00:00:00Z' - interval 1 day))": "12.52024-02-01T00:00:00Z"}, {"concat(1, true, 2.5, month('2024-03-01T00:00:00Z' - interval 1 day))": "12.52024-02-01T00:00:00Z"}, {"concat(1, true, 2.5, month('2024-03-01T00:00:00Z' - interval 1 day))": "12.52024-02-01T00:00:00Z"}, {"concat(1, true, 2.5, month('2024-03-01T00:00:00Z' - interval 1 day))": "12.52024-02-01T00:00:00Z"}], "skip": false}, {"error": null, "query": "select: nullif(1, 1)", "result_rows": [null, null, null, null, null], "skip": false}, {"error": null, "query": "select: nullif(1, 2)", "result_rows": [{"nullif(1, 2)": 1}, {"nullif(1, 2)": 1}, {"nullif(1, 2)": 1}, {"nullif(1, 2)": 1}, {"nullif(1, 2)": 1}], "skip": false}, {"error": null, "query": "select: nullif(null, 1)", "result_rows": [null, null, null, null, null], "skip": false}, {"error": null, "query": "select: nullif(1, null)", "result_rows": [{"nullif(1, null)": 1}, {"nullif(1, null)": 1}, {"nullif(1, null)": 1}, {"nullif(1, null)": 1}, {"nullif(1, null)": 1}], "skip": false}, {"error": null, "query": "select: nullif(null, null)", "result_rows": [null, null, null, null, null], "skip": false}, {"error": null, "query": "select: nullif(\"test\", \"test\")", "result_rows": [null, null, null, null, null], "skip": false}, {"error": null, "query": "select: nullif(scores.foo, 0.25)", "result_rows": [null, null, null, {"nullif(scores.foo, 0.25)": 0}, {"nullif(scores.foo, 0.25)": 1}], "skip": false}, {"error": null, "query": "select: nullif(counts.foo, -7)", "result_rows": [null, null, null, {"nullif(counts.foo, -7)": 10}, {"nullif(counts.foo, -7)": 3}], "skip": false}, {"error": null, "query": "select: nullif(metadata.nullable, true)", "result_rows": [null, null, null, null, {"nullif(metadata.nullable, true)": false}], "skip": false}, {"error": null, "query": "select: nullif(scores.foo, scores.bar)", "result_rows": [null, null, {"nullif(scores.foo, scores.bar)": 0.25}, {"nullif(scores.foo, scores.bar)": 0}, {"nullif(scores.foo, scores.bar)": 1}], "skip": false}, {"error": null, "query": "select: nullif(counts.foo, counts.bar)", "result_rows": [null, null, {"nullif(counts.foo, counts.bar)": -7}, {"nullif(counts.foo, counts.bar)": 10}, {"nullif(counts.foo, counts.bar)": 3}], "skip": false}, {"error": null, "query": "select: nullif(counts.foo < 0.5, true)", "result_rows": [null, null, null, {"nullif(counts.foo < 0.5, true)": false}, {"nullif(counts.foo < 0.5, true)": false}], "skip": false}, {"error": null, "query": "select: nullif(counts.foo < 0.5, false)", "result_rows": [null, null, null, null, {"nullif(counts.foo < 0.5, false)": true}], "skip": false}, {"error": null, "query": "select: id, scores.bar | filter: coalesce(scores.bar, 0) > 0.25", "result_rows": [{"bar": 0.5, "id": "dfa-asf"}], "skip": false}, {"error": null, "query": "select: id, scores.foo | filter: nullif(scores.foo, 0.25) is null", "result_rows": [{"foo": 0.25, "id": "uuid3"}, {"id": "uuid4"}, {"id": "uuid5"}], "skip": false}, {"error": null, "query": "select: id, scores.foo | filter: nullif(scores.foo, 0.25) is not null", "result_rows": [{"foo": 0, "id": "dfa-asf"}, {"foo": 1, "id": "asf-dfa"}], "skip": false}, {"error": null, "query": "select: id, counts.bar | filter: coalesce(counts.bar, -1) >= 0", "result_rows": [{"bar": 0, "id": "dfa-asf"}, {"bar": 2, "id": "uuid3"}], "skip": false}, {"error": null, "query": "select: id, metadata.nullable | filter: nullif(metadata.nullable, true) is null", "result_rows": [{"id": "asf-dfa", "nullable": true}, {"id": "uuid3"}, {"id": "uuid4"}, {"id": "uuid5"}], "skip": false}, {"error": null, "query": "select: id, scores.bar | filter: concat('hello', ' ', 'world') = 'hello world'", "result_rows": [{"bar": 0.5, "id": "dfa-asf"}, {"id": "asf-dfa"}, {"id": "uuid3"}, {"id": "uuid4"}, {"id": "uuid5"}], "skip": false}, {"error": null, "query": "select: id, scores.bar | filter: lower('WORLD') = 'world'", "result_rows": [{"bar": 0.5, "id": "dfa-asf"}, {"id": "asf-dfa"}, {"id": "uuid3"}, {"id": "uuid4"}, {"id": "uuid5"}], "skip": false}, {"error": null, "query": "select: id, scores.bar | filter: upper('hello') = 'HELLO'", "result_rows": [{"bar": 0.5, "id": "dfa-asf"}, {"id": "asf-dfa"}, {"id": "uuid3"}, {"id": "uuid4"}, {"id": "uuid5"}], "skip": false}, {"error": null, "query": "select: greatest(1)", "result_rows": [{"greatest(1)": 1}, {"greatest(1)": 1}, {"greatest(1)": 1}, {"greatest(1)": 1}, {"greatest(1)": 1}], "skip": false}, {"error": null, "query": "select: greatest(1, 2)", "result_rows": [{"greatest(1, 2)": 2}, {"greatest(1, 2)": 2}, {"greatest(1, 2)": 2}, {"greatest(1, 2)": 2}, {"greatest(1, 2)": 2}], "skip": false}, {"error": null, "query": "select: greatest(2, 1)", "result_rows": [{"greatest(2, 1)": 2}, {"greatest(2, 1)": 2}, {"greatest(2, 1)": 2}, {"greatest(2, 1)": 2}, {"greatest(2, 1)": 2}], "skip": false}, {"error": null, "query": "select: greatest(1.5, 2.5)", "result_rows": [{"greatest(1.5, 2.5)": 2.5}, {"greatest(1.5, 2.5)": 2.5}, {"greatest(1.5, 2.5)": 2.5}, {"greatest(1.5, 2.5)": 2.5}, {"greatest(1.5, 2.5)": 2.5}], "skip": false}, {"error": null, "query": "select: greatest(2.5, 1.5)", "result_rows": [{"greatest(2.5, 1.5)": 2.5}, {"greatest(2.5, 1.5)": 2.5}, {"greatest(2.5, 1.5)": 2.5}, {"greatest(2.5, 1.5)": 2.5}, {"greatest(2.5, 1.5)": 2.5}], "skip": false}, {"error": null, "query": "select: greatest(1, null)", "result_rows": [{"greatest(1, null)": 1}, {"greatest(1, null)": 1}, {"greatest(1, null)": 1}, {"greatest(1, null)": 1}, {"greatest(1, null)": 1}], "skip": false}, {"error": null, "query": "select: greatest(null, 1)", "result_rows": [{"greatest(null, 1)": 1}, {"greatest(null, 1)": 1}, {"greatest(null, 1)": 1}, {"greatest(null, 1)": 1}, {"greatest(null, 1)": 1}], "skip": false}, {"error": null, "query": "select: greatest(null, null)", "result_rows": [null, null, null, null, null], "skip": false}, {"error": null, "query": "select: scores.foo, greatest(scores.foo, 0.25)", "result_rows": [{"foo": 0, "greatest(scores.foo, 0.25)": 0.25}, {"foo": 0.25, "greatest(scores.foo, 0.25)": 0.25}, {"foo": 1, "greatest(scores.foo, 0.25)": 1}, {"greatest(scores.foo, 0.25)": 0.25}, {"greatest(scores.foo, 0.25)": 0.25}], "skip": false}, {"error": null, "query": "select: scores.foo, greatest(0.25, scores.foo)", "result_rows": [{"foo": 0, "greatest(0.25, scores.foo)": 0.25}, {"foo": 0.25, "greatest(0.25, scores.foo)": 0.25}, {"foo": 1, "greatest(0.25, scores.foo)": 1}, {"greatest(0.25, scores.foo)": 0.25}, {"greatest(0.25, scores.foo)": 0.25}], "skip": false}, {"error": null, "query": "select: scores.foo, greatest(scores.foo, null)", "result_rows": [null, null, {"foo": 0, "greatest(scores.foo, null)": 0}, {"foo": 0.25, "greatest(scores.foo, null)": 0.25}, {"foo": 1, "greatest(scores.foo, null)": 1}], "skip": false}, {"error": null, "query": "select: scores.foo, greatest(null, scores.foo)", "result_rows": [null, null, {"foo": 0, "greatest(null, scores.foo)": 0}, {"foo": 0.25, "greatest(null, scores.foo)": 0.25}, {"foo": 1, "greatest(null, scores.foo)": 1}], "skip": false}, {"error": null, "query": "select: counts.foo, greatest(counts.foo, -7)", "result_rows": [{"foo": -7, "greatest(counts.foo, -7)": -7}, {"foo": 10, "greatest(counts.foo, -7)": 10}, {"foo": 3, "greatest(counts.foo, -7)": 3}, {"greatest(counts.foo, -7)": -7}, {"greatest(counts.foo, -7)": -7}], "skip": false}, {"error": null, "query": "select: counts.foo, greatest(-7, counts.foo)", "result_rows": [{"foo": -7, "greatest(-7, counts.foo)": -7}, {"foo": 10, "greatest(-7, counts.foo)": 10}, {"foo": 3, "greatest(-7, counts.foo)": 3}, {"greatest(-7, counts.foo)": -7}, {"greatest(-7, counts.foo)": -7}], "skip": false}, {"error": null, "query": "select: counts.foo, greatest(counts.foo, null)", "result_rows": [null, null, {"foo": -7, "greatest(counts.foo, null)": -7}, {"foo": 10, "greatest(counts.foo, null)": 10}, {"foo": 3, "greatest(counts.foo, null)": 3}], "skip": false}, {"error": null, "query": "select: counts.foo, greatest(null, counts.foo)", "result_rows": [null, null, {"foo": -7, "greatest(null, counts.foo)": -7}, {"foo": 10, "greatest(null, counts.foo)": 10}, {"foo": 3, "greatest(null, counts.foo)": 3}], "skip": false}, {"error": null, "query": "select: scores.foo, scores.bar, greatest(scores.foo, scores.bar)", "result_rows": [null, null, {"bar": 0.5, "foo": 0, "greatest(scores.foo, scores.bar)": 0.5}, {"foo": 0.25, "greatest(scores.foo, scores.bar)": 0.25}, {"foo": 1, "greatest(scores.foo, scores.bar)": 1}], "skip": false}, {"error": null, "query": "select: counts.foo, counts.bar, greatest(counts.foo, counts.bar)", "result_rows": [null, null, {"bar": 0, "foo": 3, "greatest(counts.foo, counts.bar)": 3}, {"bar": 2, "foo": -7, "greatest(counts.foo, counts.bar)": 2}, {"foo": 10, "greatest(counts.foo, counts.bar)": 10}], "skip": false}, {"error": null, "query": "select: greatest(1, 2, 3)", "result_rows": [{"greatest(1, 2, 3)": 3}, {"greatest(1, 2, 3)": 3}, {"greatest(1, 2, 3)": 3}, {"greatest(1, 2, 3)": 3}, {"greatest(1, 2, 3)": 3}], "skip": false}, {"error": null, "query": "select: greatest(3, 2, 1)", "result_rows": [{"greatest(3, 2, 1)": 3}, {"greatest(3, 2, 1)": 3}, {"greatest(3, 2, 1)": 3}, {"greatest(3, 2, 1)": 3}, {"greatest(3, 2, 1)": 3}], "skip": false}, {"error": null, "query": "select: greatest(1, null, 3)", "result_rows": [{"greatest(1, null, 3)": 3}, {"greatest(1, null, 3)": 3}, {"greatest(1, null, 3)": 3}, {"greatest(1, null, 3)": 3}, {"greatest(1, null, 3)": 3}], "skip": false}, {"error": null, "query": "select: greatest(null, 2, 3)", "result_rows": [{"greatest(null, 2, 3)": 3}, {"greatest(null, 2, 3)": 3}, {"greatest(null, 2, 3)": 3}, {"greatest(null, 2, 3)": 3}, {"greatest(null, 2, 3)": 3}], "skip": false}, {"error": null, "query": "select: greatest(1, 2, null)", "result_rows": [{"greatest(1, 2, null)": 2}, {"greatest(1, 2, null)": 2}, {"greatest(1, 2, null)": 2}, {"greatest(1, 2, null)": 2}, {"greatest(1, 2, null)": 2}], "skip": false}, {"error": null, "query": "select: greatest(1.5, 2.5, 3.5)", "result_rows": [{"greatest(1.5, 2.5, 3.5)": 3.5}, {"greatest(1.5, 2.5, 3.5)": 3.5}, {"greatest(1.5, 2.5, 3.5)": 3.5}, {"greatest(1.5, 2.5, 3.5)": 3.5}, {"greatest(1.5, 2.5, 3.5)": 3.5}], "skip": false}, {"error": null, "query": "select: scores.foo, scores.bar, greatest(scores.foo, scores.bar, 0.5)", "result_rows": [{"bar": 0.5, "foo": 0, "greatest(scores.foo, scores.bar, 0.5)": 0.5}, {"foo": 0.25, "greatest(scores.foo, scores.bar, 0.5)": 0.5}, {"foo": 1, "greatest(scores.foo, scores.bar, 0.5)": 1}, {"greatest(scores.foo, scores.bar, 0.5)": 0.5}, {"greatest(scores.foo, scores.bar, 0.5)": 0.5}], "skip": false}, {"error": null, "query": "select: scores.foo, scores.bar, greatest(scores.foo, null, 0.5)", "result_rows": [{"bar": 0.5, "foo": 0, "greatest(scores.foo, null, 0.5)": 0.5}, {"foo": 0.25, "greatest(scores.foo, null, 0.5)": 0.5}, {"foo": 1, "greatest(scores.foo, null, 0.5)": 1}, {"greatest(scores.foo, null, 0.5)": 0.5}, {"greatest(scores.foo, null, 0.5)": 0.5}], "skip": false}, {"error": null, "query": "select: counts.foo, counts.bar, greatest(counts.foo, counts.bar, -1)", "result_rows": [{"bar": 0, "foo": 3, "greatest(counts.foo, counts.bar, -1)": 3}, {"bar": 2, "foo": -7, "greatest(counts.foo, counts.bar, -1)": 2}, {"foo": 10, "greatest(counts.foo, counts.bar, -1)": 10}, {"greatest(counts.foo, counts.bar, -1)": -1}, {"greatest(counts.foo, counts.bar, -1)": -1}], "skip": false}, {"error": null, "query": "select: counts.foo, counts.bar, greatest(null, counts.bar, -1)", "result_rows": [{"bar": 0, "foo": 3, "greatest(null, counts.bar, -1)": 0}, {"bar": 2, "foo": -7, "greatest(null, counts.bar, -1)": 2}, {"foo": 10, "greatest(null, counts.bar, -1)": -1}, {"greatest(null, counts.bar, -1)": -1}, {"greatest(null, counts.bar, -1)": -1}], "skip": false}, {"error": null, "query": "select: least(1)", "result_rows": [{"least(1)": 1}, {"least(1)": 1}, {"least(1)": 1}, {"least(1)": 1}, {"least(1)": 1}], "skip": false}, {"error": null, "query": "select: least(1, 2)", "result_rows": [{"least(1, 2)": 1}, {"least(1, 2)": 1}, {"least(1, 2)": 1}, {"least(1, 2)": 1}, {"least(1, 2)": 1}], "skip": false}, {"error": null, "query": "select: least(2, 1)", "result_rows": [{"least(2, 1)": 1}, {"least(2, 1)": 1}, {"least(2, 1)": 1}, {"least(2, 1)": 1}, {"least(2, 1)": 1}], "skip": false}, {"error": null, "query": "select: least(1.5, 2.5)", "result_rows": [{"least(1.5, 2.5)": 1.5}, {"least(1.5, 2.5)": 1.5}, {"least(1.5, 2.5)": 1.5}, {"least(1.5, 2.5)": 1.5}, {"least(1.5, 2.5)": 1.5}], "skip": false}, {"error": null, "query": "select: least(2.5, 1.5)", "result_rows": [{"least(2.5, 1.5)": 1.5}, {"least(2.5, 1.5)": 1.5}, {"least(2.5, 1.5)": 1.5}, {"least(2.5, 1.5)": 1.5}, {"least(2.5, 1.5)": 1.5}], "skip": false}, {"error": null, "query": "select: least(1, null)", "result_rows": [{"least(1, null)": 1}, {"least(1, null)": 1}, {"least(1, null)": 1}, {"least(1, null)": 1}, {"least(1, null)": 1}], "skip": false}, {"error": null, "query": "select: least(null, 1)", "result_rows": [{"least(null, 1)": 1}, {"least(null, 1)": 1}, {"least(null, 1)": 1}, {"least(null, 1)": 1}, {"least(null, 1)": 1}], "skip": false}, {"error": null, "query": "select: least(null, null)", "result_rows": [null, null, null, null, null], "skip": false}, {"error": null, "query": "select: scores.foo, least(scores.foo, 0.25)", "result_rows": [{"foo": 0, "least(scores.foo, 0.25)": 0}, {"foo": 0.25, "least(scores.foo, 0.25)": 0.25}, {"foo": 1, "least(scores.foo, 0.25)": 0.25}, {"least(scores.foo, 0.25)": 0.25}, {"least(scores.foo, 0.25)": 0.25}], "skip": false}, {"error": null, "query": "select: scores.foo, least(0.25, scores.foo)", "result_rows": [{"foo": 0, "least(0.25, scores.foo)": 0}, {"foo": 0.25, "least(0.25, scores.foo)": 0.25}, {"foo": 1, "least(0.25, scores.foo)": 0.25}, {"least(0.25, scores.foo)": 0.25}, {"least(0.25, scores.foo)": 0.25}], "skip": false}, {"error": null, "query": "select: scores.foo, least(scores.foo, null)", "result_rows": [null, null, {"foo": 0, "least(scores.foo, null)": 0}, {"foo": 0.25, "least(scores.foo, null)": 0.25}, {"foo": 1, "least(scores.foo, null)": 1}], "skip": false}, {"error": null, "query": "select: scores.foo, least(null, scores.foo)", "result_rows": [null, null, {"foo": 0, "least(null, scores.foo)": 0}, {"foo": 0.25, "least(null, scores.foo)": 0.25}, {"foo": 1, "least(null, scores.foo)": 1}], "skip": false}, {"error": null, "query": "select: counts.foo, least(counts.foo, -7)", "result_rows": [{"foo": -7, "least(counts.foo, -7)": -7}, {"foo": 10, "least(counts.foo, -7)": -7}, {"foo": 3, "least(counts.foo, -7)": -7}, {"least(counts.foo, -7)": -7}, {"least(counts.foo, -7)": -7}], "skip": false}, {"error": null, "query": "select: counts.foo, least(-7, counts.foo)", "result_rows": [{"foo": -7, "least(-7, counts.foo)": -7}, {"foo": 10, "least(-7, counts.foo)": -7}, {"foo": 3, "least(-7, counts.foo)": -7}, {"least(-7, counts.foo)": -7}, {"least(-7, counts.foo)": -7}], "skip": false}, {"error": null, "query": "select: counts.foo, least(counts.foo, null)", "result_rows": [null, null, {"foo": -7, "least(counts.foo, null)": -7}, {"foo": 10, "least(counts.foo, null)": 10}, {"foo": 3, "least(counts.foo, null)": 3}], "skip": false}, {"error": null, "query": "select: counts.foo, least(null, counts.foo)", "result_rows": [null, null, {"foo": -7, "least(null, counts.foo)": -7}, {"foo": 10, "least(null, counts.foo)": 10}, {"foo": 3, "least(null, counts.foo)": 3}], "skip": false}, {"error": null, "query": "select: scores.foo, scores.bar, least(scores.foo, scores.bar)", "result_rows": [null, null, {"bar": 0.5, "foo": 0, "least(scores.foo, scores.bar)": 0}, {"foo": 0.25, "least(scores.foo, scores.bar)": 0.25}, {"foo": 1, "least(scores.foo, scores.bar)": 1}], "skip": false}, {"error": null, "query": "select: counts.foo, counts.bar, least(counts.foo, counts.bar)", "result_rows": [null, null, {"bar": 0, "foo": 3, "least(counts.foo, counts.bar)": 0}, {"bar": 2, "foo": -7, "least(counts.foo, counts.bar)": -7}, {"foo": 10, "least(counts.foo, counts.bar)": 10}], "skip": false}, {"error": null, "query": "select: least(1, 2, 3)", "result_rows": [{"least(1, 2, 3)": 1}, {"least(1, 2, 3)": 1}, {"least(1, 2, 3)": 1}, {"least(1, 2, 3)": 1}, {"least(1, 2, 3)": 1}], "skip": false}, {"error": null, "query": "select: least(3, 2, 1)", "result_rows": [{"least(3, 2, 1)": 1}, {"least(3, 2, 1)": 1}, {"least(3, 2, 1)": 1}, {"least(3, 2, 1)": 1}, {"least(3, 2, 1)": 1}], "skip": false}, {"error": null, "query": "select: least(1, null, 3)", "result_rows": [{"least(1, null, 3)": 1}, {"least(1, null, 3)": 1}, {"least(1, null, 3)": 1}, {"least(1, null, 3)": 1}, {"least(1, null, 3)": 1}], "skip": false}, {"error": null, "query": "select: least(null, 2, 3)", "result_rows": [{"least(null, 2, 3)": 2}, {"least(null, 2, 3)": 2}, {"least(null, 2, 3)": 2}, {"least(null, 2, 3)": 2}, {"least(null, 2, 3)": 2}], "skip": false}, {"error": null, "query": "select: least(1, 2, null)", "result_rows": [{"least(1, 2, null)": 1}, {"least(1, 2, null)": 1}, {"least(1, 2, null)": 1}, {"least(1, 2, null)": 1}, {"least(1, 2, null)": 1}], "skip": false}, {"error": null, "query": "select: least(1.5, 2.5, 3.5)", "result_rows": [{"least(1.5, 2.5, 3.5)": 1.5}, {"least(1.5, 2.5, 3.5)": 1.5}, {"least(1.5, 2.5, 3.5)": 1.5}, {"least(1.5, 2.5, 3.5)": 1.5}, {"least(1.5, 2.5, 3.5)": 1.5}], "skip": false}, {"error": null, "query": "select: scores.foo, scores.bar, least(scores.foo, scores.bar, 0.5)", "result_rows": [{"bar": 0.5, "foo": 0, "least(scores.foo, scores.bar, 0.5)": 0}, {"foo": 0.25, "least(scores.foo, scores.bar, 0.5)": 0.25}, {"foo": 1, "least(scores.foo, scores.bar, 0.5)": 0.5}, {"least(scores.foo, scores.bar, 0.5)": 0.5}, {"least(scores.foo, scores.bar, 0.5)": 0.5}], "skip": false}, {"error": null, "query": "select: scores.foo, scores.bar, least(scores.foo, null, 0.5)", "result_rows": [{"bar": 0.5, "foo": 0, "least(scores.foo, null, 0.5)": 0}, {"foo": 0.25, "least(scores.foo, null, 0.5)": 0.25}, {"foo": 1, "least(scores.foo, null, 0.5)": 0.5}, {"least(scores.foo, null, 0.5)": 0.5}, {"least(scores.foo, null, 0.5)": 0.5}], "skip": false}, {"error": null, "query": "select: counts.foo, counts.bar, least(counts.foo, counts.bar, -1)", "result_rows": [{"bar": 0, "foo": 3, "least(counts.foo, counts.bar, -1)": -1}, {"bar": 2, "foo": -7, "least(counts.foo, counts.bar, -1)": -7}, {"foo": 10, "least(counts.foo, counts.bar, -1)": -1}, {"least(counts.foo, counts.bar, -1)": -1}, {"least(counts.foo, counts.bar, -1)": -1}], "skip": false}, {"error": null, "query": "select: counts.foo, counts.bar, least(null, counts.bar, -1)", "result_rows": [{"bar": 0, "foo": 3, "least(null, counts.bar, -1)": -1}, {"bar": 2, "foo": -7, "least(null, counts.bar, -1)": -1}, {"foo": 10, "least(null, counts.bar, -1)": -1}, {"least(null, counts.bar, -1)": -1}, {"least(null, counts.bar, -1)": -1}], "skip": false}]