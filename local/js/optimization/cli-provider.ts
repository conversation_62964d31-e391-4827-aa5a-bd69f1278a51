import {
  _internalGetGlobalState,
  Dataset,
  EvalResultWithSummary,
  Experiment,
  initDataset,
  initExperiment,
  initFunction,
  newId,
  runEvaluator,
  Span,
} from "braintrust";
import { MetaEval } from "./evals/meta-eval";
import {
  applyEdits,
  ContinueExecutionToolParameters,
  EditDataToolParameters,
  EditTaskToolParameters,
  EditTaskToolResult,
  GetSummaryToolResult,
  GetResultsToolParameters,
  GetResultsToolResult,
  isInternalMetric,
  RerunTaskToolParameters,
  RerunTaskToolResult,
  roundNumber,
  ScoreSummary,
} from "./tools";
import { ToolManager } from "./tools/registry";
import { Spinner } from "./cli";
import { GetAvailableScorersResult } from "./tools/get-scorers";
import { Evaluators } from "autoevals";
import {
  EditScorersParams,
  EditScorersToolResult,
  getGlobalScorers,
} from "./tools/edit-scorers";
import { CreateCodeScorerParams } from "./tools/create-scorer";
import slugifyLib from "slugify";
import { v4 as uuidv4 } from "uuid";
import { PageKey } from "./llm/system-prompt";

export async function makeTools({
  def,
  projectName,
  logProjectName,
  spinner,
  printExperimentResult,
  deterministic,
  model,
  applicationContext = "playground",
  preLoadedExperiment,
}: {
  def: MetaEval;
  projectName: string;
  logProjectName: string;
  spinner?: Spinner;
  printExperimentResult?: (result: string) => void;
  deterministic?: boolean;
  model: string;
  //This exists to be able to simulate running Loop in different parts of the application.
  //Based on the page, Loop has access to different tools or same tools but with different parameters.
  //If left out, it defaults to playground which aligns with the behavior of the CLI as it was originally written.
  applicationContext?: PageKey;
  preLoadedExperiment?: Experiment;
}): Promise<ToolManager> {
  const tools = new ToolManager(model.includes("gemini"));
  let lastSummary: EvalResultWithSummary<unknown, unknown, unknown> | null =
    null;
  const task = def.task.copy();
  // If we have an initial experiment, load its data into lastSummary. This is to emulate experiment page.
  if (preLoadedExperiment) {
    try {
      const summary = await preLoadedExperiment.summarize();
      const results = [];

      // Fetch the experiment results
      for await (const record of preLoadedExperiment.fetch()) {
        if (record.root_span_id !== record.span_id) {
          continue; // Skip non-root spans
        }

        results.push({
          id: record.id,
          input: record.input,
          output: record.output,
          expected: record.expected,
          metadata: record.metadata,
          scores: record.scores || {},
          metrics: record.metrics || {},
          error: undefined,
          origin: {
            object_type: "experiment" as const,
            object_id: await preLoadedExperiment.id,
            id: record.id,
          },
        });
      }

      lastSummary = new EvalResultWithSummary(summary, results);
    } catch (error) {
      console.warn("Failed to load initial experiment data:", error);
    }
  }

  const initialData = initDataset({
    project: projectName,
    dataset: def.dataset,
  });

  //Adding uuid to the dataset name to avoid things being added to the same dataset when running multiple evals in parallel.
  const datasetName = `${def.dataset}-${def.name}-${new Date().toISOString()}-${uuidv4()}`;
  const dataset = initDataset({
    project: logProjectName,
    dataset: datasetName,
  });

  const logProjectId = (await dataset.project).id;

  for await (const datum of initialData) {
    dataset.insert({
      id: datum.id,
      tags: datum.tags,
      input: datum.input,
      expected: datum.expected,
      metadata: datum.metadata ?? undefined,
    });
  }
  await dataset.flush();

  tools.updateImplementations({
    get_summary: async (): Promise<GetSummaryToolResult[]> => {
      if (applicationContext === "experiments") {
        return [
          {
            taskName: def.name,
            definition: {},
            index: 0,
            ...(lastSummary
              ? computeLastScoresAndMetrics(lastSummary)
              : { scores: {}, metrics: {} }),
          },
        ];
      }
      return [
        {
          taskName: "test",
          definition: task.serialize(),
          index: 0,
          ...(lastSummary
            ? computeLastScoresAndMetrics(lastSummary)
            : { scores: {}, metrics: {} }),
        },
      ];
    },
    get_results: async (
      params: GetResultsToolParameters,
    ): Promise<GetResultsToolResult[]> => {
      if (applicationContext === "playground") {
        return getPlaygroundResults(
          params,
          lastSummary,
          deterministic ?? false,
        );
      } else if (applicationContext === "dataset") {
        return getDatasetResults(params, dataset, deterministic ?? false);
      } else {
        throw new Error(
          "Cannot get results for page other than playground or dataset",
        );
      }
    },
    edit_task: async (
      params: EditTaskToolParameters,
      span: Span,
    ): Promise<EditTaskToolResult> => {
      const definitionData = task.serialize();
      const newDefinitionData = applyEdits(definitionData, params.edits, span);
      task.deserialize(newDefinitionData);
      return { ok: true };
    },
    run_task: async (
      params: RerunTaskToolParameters,
      span: Span,
    ): Promise<RerunTaskToolResult> => {
      if (params.index !== 0) {
        throw new Error("Cannot rerun task with index other than 0");
      }

      // This is important because it'll get us _xact_id for the dataset rows
      dataset.clearCache();

      let expInc = 0;
      let expTotal = 0;

      lastSummary = await runEvaluator(
        initExperiment(logProjectName, {
          experiment: def.name,
          description: def.description,
          dataset,
        }),
        {
          projectName,
          evalName: def.name,
          data: dataset,
          task: async (input, hook) => {
            return await task.execute(input, hook);
          },
          scores: def.scores,
        },
        {
          start: (name, total) => {
            expTotal = total;
            spinner?.start(`Running ${name}... (0/${total})`);
          },
          stop: () => {},
          increment: (name) => {
            expInc += 1;
            if (spinner) {
              spinner.text = `Running ${name}... (${expInc}/${expTotal})`;
            }
          },
        },
        [],
        undefined,
        undefined,
      );

      if (spinner) {
        spinner.text = "";
        spinner.succeed("Done!");
      }

      if (lastSummary.summary.experimentUrl && printExperimentResult) {
        span.log({
          metadata: {
            experiment_url: lastSummary.summary.experimentUrl,
          },
        });
        printExperimentResult(
          `View experiment at:\n${lastSummary.summary.experimentUrl}`,
        );
      }
      return {
        summary: {
          taskName: def.name,
          index: 0,
          ...computeLastScoresAndMetrics(lastSummary),
        },
      };
    },
    // BTQL-TODO: Re-enable this tool once we have a good tool for BTQLs
    // edit_btql: async (params: EditBTQLToolParameters) => {
    //   return { ok: true };
    // },
    edit_data: async (params: EditDataToolParameters) => {
      for (const edit of params.edits) {
        if (!edit.id) {
          const metadata = {
            ...(edit.metadata ?? {}),
            ...(applicationContext === "dataset" ? { synthetic: true } : {}),
          };

          const data = {
            id: newId(),
            tags: undefined,
            input: edit.input,
            expected: edit.expected,
            metadata: metadata ?? undefined,
          };
          dataset.insert(data);
        } else {
          if (edit.delete) {
            dataset.delete(edit.id);
          } else {
            const metadata = {
              ...(edit.metadata ?? {}),
              ...(applicationContext === "dataset" ? { synthetic: true } : {}),
            };

            const data = {
              id: edit.id,
              ...(edit.input !== undefined ? { input: edit.input } : {}),
              ...(edit.expected !== undefined
                ? { expected: edit.expected }
                : {}),
              metadata: metadata ?? undefined,
            };
            dataset.update(data);
          }
        }
      }
      await dataset.flush();

      // Clear the dataset cache to ensure subsequent reads get the updated data
      dataset.clearCache();

      return { ok: true };
    },
    get_available_scorers: async (): Promise<GetAvailableScorersResult[]> => {
      return getGlobalScorers();
    },
    edit_scorers: async (
      params: EditScorersParams,
    ): Promise<EditScorersToolResult> => {
      for (const scorerParam of params.scorers) {
        const scorer = Evaluators.flatMap((category) => category.methods).find(
          (method) => method.method.name === scorerParam.id,
        );
        if (!scorer) {
          throw new Error(`Scorer ${scorerParam.id} not found`);
        }
        if (scorerParam.enabled) {
          // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
          if (!def.scores.some((s) => (s as unknown) === scorer)) {
            def.scores.push(scorer.method);
          }
        } else {
          // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
          def.scores = def.scores.filter((s) => (s as unknown) !== scorer);
        }
      }
      return { ok: true };
    },
    create_code_scorer: async (params: CreateCodeScorerParams) => {
      const apiConn = _internalGetGlobalState().apiConn();
      const id = newId();
      const slug =
        slugifyLib(params.name, { lower: true, strict: true }) +
        "-" +
        uuidv4().slice(-4);

      await apiConn.post("/v1/function", {
        id,
        project_id: logProjectId,
        function_data: {
          type: "code",
          data: {
            type: "inline",
            runtime_context:
              params.runtime === "typescript"
                ? { runtime: "node", version: "20" }
                : {
                    runtime: "python",
                    version: "3.11",
                  },
            code: params.code,
          },
        },
        name: params.name,
        slug,
        function_type: "scorer",
      });

      def.scores.push(
        initFunction({
          projectName: logProjectName,
          slug,
        }),
      );

      return { ok: true };
    },
    continue_execution: async (params: ContinueExecutionToolParameters) => {
      return { allowed: false };
    },
  });
  return tools;
}

function computeLastScoresAndMetrics(
  summary: EvalResultWithSummary<unknown, unknown, unknown>,
): {
  scores: Record<string, ScoreSummary>;
  metrics: Record<string, ScoreSummary>;
} {
  const scores: Record<string, ScoreSummary> = {};
  const metrics: Record<string, ScoreSummary> = {};

  for (const [name, score] of Object.entries(summary.summary.scores)) {
    scores[name] = {
      avg: roundNumber(score.score),
      min: undefined,
      max: undefined,
    };
  }
  for (const [name, metric] of Object.entries(summary.summary.metrics ?? {})) {
    if (isInternalMetric(name)) {
      continue;
    }
    metrics[name] = {
      avg: roundNumber(metric.metric),
      min: undefined,
      max: undefined,
    };
  }

  for (const result of summary.results) {
    if (result.scores) {
      for (const [key, value] of Object.entries(result.scores)) {
        const v = roundNumber(value);
        if (v !== undefined && scores[key] !== undefined) {
          scores[key].max = Math.max(scores[key].max ?? 0, v);
          scores[key].min = Math.min(scores[key].min ?? 0, v);
        }
      }
    }
  }

  return { scores, metrics };
}

function getUniqueRandomNumbers(setSize: number, range: number) {
  if (setSize >= range) {
    return Array.from({ length: range }, (_, i) => i);
  }
  const nums = Array.from({ length: range }, (_, i) => i);
  for (let i = nums.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [nums[i], nums[j]] = [nums[j], nums[i]]; // Fisher–Yates shuffle
  }
  return nums.slice(0, setSize);
}

async function getDatasetResults(
  params: GetResultsToolParameters,
  dataset: Dataset,
  deterministic: boolean,
) {
  if (params.numSamples === 0) {
    return [];
  }

  const results: GetResultsToolResult[] = [];
  let count = 0;

  for await (const row of dataset.fetch()) {
    const result = {
      id: row.id,
      input: row.input,
      output: undefined,
      metadata: row.metadata,
      expected: row.expected,
      scores: {},
      metrics: {},
    };

    if (deterministic) {
      // If deterministic, take first N rows
      if (results.length < params.numSamples) {
        results.push(result);
      } else {
        break;
      }
    } else {
      if (results.length < params.numSamples) {
        results.push(result);
      } else {
        // Randomly decide whether to include this row
        const j = Math.floor(Math.random() * (count + 1));
        if (j < params.numSamples) {
          results[j] = result;
        }
      }
    }
    count++;
  }
  return results;
}

function getPlaygroundResults(
  params: GetResultsToolParameters,
  lastSummary: EvalResultWithSummary<unknown, unknown, unknown> | null,
  deterministic: boolean,
) {
  if (params.index !== 0) {
    throw new Error("Cannot get results for task with index other than 0");
  } else if (!lastSummary) {
    return [];
  }

  const lastResults = lastSummary.results;

  // Generate params.numSamples random indices between 0 and data.length - 1
  const indices = deterministic
    ? Array.from(
        { length: Math.min(params.numSamples, lastResults.length) },
        (_, i) => i,
      )
    : getUniqueRandomNumbers(params.numSamples, lastResults.length);
  const indexArr = Array.from(indices);
  const results: GetResultsToolResult[] = [];
  for (const index of indexArr) {
    const output = lastResults[index];
    if (!output.origin) {
      throw new Error("Origin not found (this is likely a bug)");
    }
    const result: GetResultsToolResult = {
      id: output.origin?.id ?? "",
      input: output.input,
      output: output.output,
      metadata: "metadata" in output ? output.metadata : undefined,
      expected: output.expected,
      // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
      scores: Object.fromEntries(
        Object.entries(output?.scores ?? {})
          .filter(([_, score]) => score !== null)
          .map(([key, score]) => [key, roundNumber(score)]),
      ) as Record<string, number>,
      metrics: {}, // TODO: The SDK doesn't give us these metrics.
    };
    results.push(result);
  }
  return results;
}
