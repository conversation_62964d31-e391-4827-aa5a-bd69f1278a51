import os

import braintrust
from strands import Agent
from strands.models.litellm import LiteLLMModel
from strands.telemetry import StrandsTelemetry
from strands_tools import calculator

# Configure environment
api_url = os.getenv("BRAINTRUST_API_URL", "https://api.braintrust.dev")
api_key = os.getenv("BRAINTRUST_API_KEY")
project_name = os.getenv("BRAINTRUST_PROJECT_NAME", "calculator-example")

if api_url:
    os.environ["BRAINTRUST_API_URL"] = api_url
if api_key:
    os.environ["BRAINTRUST_API_KEY"] = api_key

# Initialize Braintrust
bt_project = braintrust.init_logger(project=project_name)
print(f"Using project: {project_name}")

# Configure Strands telemetry to send to Braintrust
os.environ["OTEL_EXPORTER_OTLP_ENDPOINT"] = f"{api_url}/otel"
headers = []
if api_key:
    headers.append(f"authorization=Bearer {api_key}")
if project_name and bt_project:
    headers.append(f"x-bt-parent=project_name:{project_name}")
if headers:
    os.environ["OTEL_EXPORTER_OTLP_HEADERS"] = ",".join(headers)
    print(f"OTEL headers: {os.environ['OTEL_EXPORTER_OTLP_HEADERS']}")

strands_telemetry = StrandsTelemetry()
strands_telemetry.setup_otlp_exporter()
strands_telemetry.setup_console_exporter()  # Also show console output

model = LiteLLMModel(model_id="openai/gpt-4o")

agent = Agent(
    model=model,
    system_prompt="You are an AI agent. Help answer questions with the tools at your disposal.",
    tools=[calculator],
)

result = agent("What is 123987 * 23498234?")
print(f"Result: {result}")
