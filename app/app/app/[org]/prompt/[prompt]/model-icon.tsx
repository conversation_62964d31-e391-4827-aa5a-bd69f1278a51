import {
  Anthropic,
  Azure,
  Amazon,
  Groq,
  HuggingFace,
  Meta,
  Mistral,
  OpenAI,
  Perplexity,
  Replicate,
  Cohere,
  DeepSeek,
  Qwen,
  Gemini,
  XAI,
  Nvidia,
  Microsoft,
  Databricks,
} from "#/ui/icons/providers";
import { cn } from "#/utils/classnames";
import { Chrome, Box, FlaskConical } from "lucide-react";
import { AvailableModels } from "@braintrust/proxy/schema";

export const getModelIcon = (model: string) => {
  model = model.replace(/^publishers\/(\w+)\/models\//, "$1/").toLowerCase();
  model = model.replace(/^accounts\/fireworks\/models\//, "").toLowerCase();
  model = model.replace(/^databricks-/, "").toLowerCase();
  model = model.replace(/^(us|apac|eu)\./, "").toLowerCase();

  if (model.startsWith("gpt-35")) {
    return Azure;
  }
  if (
    model.startsWith("gpt") ||
    model.startsWith("chatgpt") ||
    model.startsWith("o1") ||
    model.startsWith("o3") ||
    model.startsWith("o4") ||
    model.startsWith("text-davinci") ||
    model.includes("gpt-oss")
  ) {
    return OpenAI;
  }
  if (model.startsWith("claude") || model.startsWith("anthropic")) {
    return Anthropic;
  }
  if (
    model.startsWith("meta") ||
    model.startsWith("llama") ||
    model.startsWith("codellama")
  ) {
    return Meta;
  }
  if (
    model.startsWith("mistral") ||
    model.startsWith("mixtral") ||
    model.startsWith("codestral") ||
    model.startsWith("magistral") ||
    model.startsWith("devstral") ||
    model.startsWith("pixtral") ||
    model.startsWith("ministral") ||
    model.startsWith("open-mistral") ||
    model.startsWith("open-codestral")
  ) {
    return Mistral;
  }
  if (
    model.startsWith("nous") ||
    model.startsWith("gryphe") ||
    model.startsWith("dolphin")
  ) {
    return HuggingFace;
  }
  if (model.startsWith("sonar") || model === "r1-1776") {
    return Perplexity;
  }
  if (
    model.startsWith("google") ||
    model.startsWith("gemini") ||
    model.startsWith("gemma") ||
    model.startsWith("learnlm")
  ) {
    return Gemini;
  }
  if (model.startsWith("groq")) {
    return Groq;
  }
  if (model.startsWith("replicate")) {
    return Replicate;
  }
  if (model.startsWith("window")) {
    return Chrome;
  }
  if (model.startsWith("amazon")) {
    return Amazon;
  }
  if (model.startsWith("cohere")) {
    return Cohere;
  }
  if (model.startsWith("deepseek")) {
    return DeepSeek;
  }
  if (model.startsWith("qwen") || model.startsWith("qwq")) {
    return Qwen;
  }
  if (model.startsWith("grok")) {
    return XAI;
  }
  if (model.startsWith("nvidia")) {
    return Nvidia;
  }
  if (
    model.startsWith("microsoft") ||
    model.startsWith("phi") ||
    model.startsWith("wizardlm")
  ) {
    return Microsoft;
  }
  if (model.startsWith("databricks")) {
    return Databricks;
  }
  return Box;
};

export const ModelOptionLabel = ({
  model,
  displayName,
  modelNotFound,
  className,
  labelClassName,
  deprecated,
  experimental,
}: {
  model: string;
  displayName?: string | null;
  modelNotFound?: boolean;
  className?: string;
  labelClassName?: string;
  deprecated?: boolean | null;
  experimental?: boolean | null;
}) => {
  const Icon = getModelIcon(model);
  const isDefaultIcon = Icon === Box;

  // Look up display name if not provided
  const effectiveDisplayName =
    displayName || AvailableModels[model]?.displayName || model;

  return (
    <span
      className={cn(
        "flex items-center gap-2",
        {
          "w-full flex-1": !modelNotFound,
        },
        className,
      )}
    >
      <Icon
        size={18}
        className={cn({
          "text-primary-400": isDefaultIcon,
        })}
      />
      <span className={cn("flex-1", labelClassName)}>
        {effectiveDisplayName}
      </span>
      {deprecated && (
        <span className="text-xs text-primary-400">Deprecated</span>
      )}
      {experimental && <FlaskConical className="size-3 text-primary-400" />}
    </span>
  );
};
