import type { NextApiRequest, NextApiResponse } from "next";
import {
  invokeServiceRoleSupabaseUdf,
  udfOrgIdSchema,
} from "../_invoke_supabase_udf";
import { createProjectSchema, projectSchema } from "@braintrust/core/typespecs";
import { z } from "zod";

const paramsSchema = createProjectSchema
  .omit({ name: true })
  .extend({
    project_name: createProjectSchema.shape.name,
  })
  .merge(udfOrgIdSchema);

export default async function get_token_api(
  req: NextApiRequest,
  res: NextApiResponse,
) {
  await invokeServiceRoleSupabaseUdf(req, res, "register_project", {
    removeFields: ["update"],
    paramsSchema,
    outputSchema: z.object({ project: projectSchema }),
    argnames: ["auth_id", "project_name", "org_id"],
    makeCacheKey: (args) => `project:register:${args.project_name}`,
  });
}
