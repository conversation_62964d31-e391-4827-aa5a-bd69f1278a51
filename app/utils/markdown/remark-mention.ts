import { visit } from "unist-util-visit";
import { mentionRegexGlobal } from "#/utils/mentions";

export default function remarkMention() {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  return (tree: any) => {
    try {
      visit(tree, "text", (node, index, parent) => {
        if (!parent || typeof node.value !== "string") return;

        const matches = [...node.value.matchAll(mentionRegexGlobal)];

        if (matches.length === 0) return;

        const newNodes: unknown[] = [];
        let lastIndex = 0;

        for (const match of matches) {
          const [full] = match;
          const start = match.index ?? 0;
          const end = start + full.length;

          if (start > lastIndex) {
            newNodes.push({
              type: "text",
              value: node.value.slice(lastIndex, start),
            });
          }

          const mentionNode = {
            type: "text",
            value: match["0"],
          };
          newNodes.push(mentionNode);
          lastIndex = end;
        }

        if (lastIndex < node.value.length) {
          newNodes.push({
            type: "text",
            value: node.value.slice(lastIndex),
          });
        }

        parent.children.splice(index, 1, ...newNodes);
      });
    } catch {
      console.error("Error parsing remarkMention");
      return;
    }
  };
}
