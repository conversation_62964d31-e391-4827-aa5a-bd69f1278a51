import { type FetchOrgUsersOutput } from "./org-users";
import { resend } from "#/lib/resend";
import CommentMention from "#/ui/email-templates/comment-mention";
import { createElement } from "react";

const userIdRegex = "[a-zA-Z0-9-]*";
export const mentionRegex = new RegExp(`@${userIdRegex}`);
export const mentionRegexGlobal = new RegExp(`@${userIdRegex}`, "g");
export const mentionRegexCapture = new RegExp(`@(${userIdRegex})`);
export const mentionRegexCaptureGlobal = new RegExp(`@(${userIdRegex})`, "g");

export const userIdMatchRegex =
  /[a-zA-Z0-9]{8}-[a-zA-Z0-9]{4}-[a-zA-Z0-9]{4}-[a-zA-Z0-9]{4}-[a-zA-Z0-9]{12}/;

export function parseMentions({
  text,
  formatMention,
  orgUsers,
}: {
  text: string;
  formatMention?: (mention: string) => string;
  orgUsers: FetchOrgUsersOutput;
}) {
  return text.replace(mentionRegexCaptureGlobal, (match, userId) => {
    const user = orgUsers[userId];
    if (user) {
      const displayName =
        user.given_name && user.family_name
          ? `${user.given_name} ${user.family_name}`
          : user.email || userId;
      return formatMention
        ? formatMention(`@${displayName}`)
        : `@${displayName}`;
    } else if (userIdMatchRegex.test(match)) {
      return "@Unknown";
    }
    return match;
  });
}

export async function sendCommentMention({
  orgName,
  commentText,
  mentionerName,
  emails,
  projectName,
  commentId,
  commentLink,
  entityType,
  entityName,
}: {
  orgName: string;
  commentText: string;
  mentionerName: string;
  emails: string[];
  projectName?: string;
  commentId: string;
  commentLink: string;
  entityType?: string | null;
  entityName?: string | null;
}): Promise<null> {
  if (!resend || emails.length === 0) {
    return null;
  }
  const subject = `Re: ${orgName}/${projectName} - ${entityType === "logs" ? "Project logs" : entityName}`;

  const batchEmails = emails.map((email) => ({
    from: `Braintrust <<EMAIL>>`,
    to: email,
    subject,
    react: createElement(
      CommentMention,
      {
        mentionerName,
        projectName,
        entityType,
        entityName,
        comment: commentText,
        commentLink,
      },
      null,
    ),
  }));

  const resp = await resend.batch.send(batchEmails, {
    // Prevents duplicate emails from being sent. Resend will not send the email if the idempotencyKey has been used within the last 24 hours.
    idempotencyKey: `${emails.join("-")}-${mentionerName}-${commentId}`,
  });
  if (resp.error) {
    throw new Error(resp.error.message);
  }

  return null;
}
