.cm-editor {
  height: 100%;
  width: 100%;
  outline: none;
  background-color: transparent;
}

.cm-editor.cm-focused {
  outline: inherit;
}

.cm-editor .cm-content {
  font-family: inherit;
  caret-color: inherit;
  padding: inherit;
}

.cm-editor .cm-line {
  padding: 0;
}

.cm-focused .cm-cursor {
  all: inherit;
}

.cm-panels {
  display: none;
}

.cm-search {
  display: none;
}

.cm-tooltip.cm-tooltip-hover {
  @apply !bg-background !border-primary-100 !rounded-md !shadow-md !border;
}

.cm-tooltip.cm-tooltip-hover .cm-tooltip-section:not(:first-child) {
  @apply border-primary-200 !border-t;
}

.cm-tooltip.cm-tooltip-hover .cm-diagnostic-error {
  @apply p-3 text-sm text-primary-800 max-w-md flex flex-wrap gap-3 items-start;
}

.cm-tooltip.cm-tooltip-hover .cm-diagnostic-error .cm-diagnosticText {
  @apply w-full;
}

.cm-tooltip.cm-tooltip-hover .cm-diagnostic-error button {
  @apply block;
}

.cm-tooltip.cm-tooltip-autocomplete {
  @apply border-none;
}

.cm-tooltip.cm-tooltip-autocomplete ul {
  @apply rounded-md bg-background !border !border-primary-200 ring-0;
}

.cm-tooltip.cm-tooltip-autocomplete li {
  @apply !py-1.5 !px-2 !text-primary-700;
}

.cm-tooltip.cm-tooltip-autocomplete li[aria-selected="true"] {
  @apply !bg-primary-100/80;
}

.cm-tooltip.cm-tooltip-autocomplete .cm-completionDetail {
  all: initial;
  @apply text-xs block text-primary-500 font-inter m-0 p-0 pt-1 !font-normal;
}

.cm-tooltip.cm-tooltip-hover .cm-diagnostic-error .cm-diagnosticAction {
  border-style: solid !important;
  @apply font-medium text-xs px-2 bg-transparent border border-primary-200 rounded-md text-primary-800 hover:border-primary-300 m-0;
}

.cm-foldGutter span[title="Fold line"] {
  transform: translateY(-3px);
  position: absolute;
}

.linkable-gutter .cm-gutterElement:hover {
  @apply cursor-pointer text-primary-800;
}

.linkable-gutter .cm-selectedLine {
  @apply bg-yellow-200/50 dark:bg-yellow-950/50 text-primary-800 rounded-sm;
}

[class*="cm-merge-"] {
  .cm-content {
    @apply !pl-0;
  }

  .cm-gutter {
    .cm-gutterElement.cm-deletedLineGutter {
      @apply bg-bad-400 dark:bg-bad-300;
    }
    .cm-gutterElement.cm-addedLineGutter {
      @apply bg-good-300;
    }
  }

  .cm-changedLine.cm-line {
    @apply bg-good-50/50 dark:bg-good-50/50;

    .cm-changedText {
      background: none;
      @apply bg-good-200/60 dark:bg-good-200/50;
    }
  }

  .cm-deletedChunk {
    @apply bg-bad-50/80 dark:bg-bad-50/30;
    .cm-deletedText {
      background: none;
      @apply bg-bad-200/60 dark:bg-bad-200/40;
    }

    del {
      text-decoration: none;
    }
  }
}

.cm-mention {
  @apply text-accent-600 font-medium;
}

.cm-completionIcon-user {
  @apply !hidden;
}

.cm-tooltip-autocomplete ul li {
  @apply !cursor-pointer;
}

.cm-completionLabel {
  @apply font-medium font-inter text-sm text-primary-800 m-0 p-0;
}

.cm-completionDetail {
  @apply !pointer-events-none !font-normal;
}
