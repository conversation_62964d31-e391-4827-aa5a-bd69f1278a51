import React from "react";
import { But<PERSON> } from "#/ui/button";
import { ChevronLeftIcon, ChevronRightIcon, UserIcon } from "@heroicons/react/24/outline";
import { cn } from "#/utils/cn";
import { useUserTraceNavigation } from "./use-user-trace-navigation";
import { type RowId } from "#/utils/diffs/diff-objects";
import { Tooltip, TooltipContent, TooltipTrigger } from "#/ui/tooltip";

interface UserTraceNavigationProps {
  objectType: string;
  objectId: string | null;
  currentRowId: RowId | null;
  className?: string;
}

export function UserTraceNavigation({
  objectType,
  objectId,
  currentRowId,
  className,
}: UserTraceNavigationProps) {
  const {
    userTraces,
    currentUserTraceIndex,
    hasNextUserTrace,
    hasPrevUserTrace,
    onNextUserTrace,
    onPrevUserTrace,
    isLoading,
    currentUser,
  } = useUserTraceNavigation({
    objectType,
    objectId,
    currentRowId,
  });

  // Don't show navigation if there's no user or only one trace
  if (!currentUser || userTraces.length <= 1) {
    return null;
  }

  return (
    <div className={cn("flex items-center gap-2", className)}>
      <div className="flex items-center gap-1 text-sm text-muted-foreground">
        <UserIcon className="h-4 w-4" />
        <span className="font-medium">{currentUser}</span>
        <span className="text-xs">
          ({currentUserTraceIndex + 1} of {userTraces.length})
        </span>
      </div>
      
      <div className="flex items-center gap-1">
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              variant="ghost"
              size="sm"
              onClick={onPrevUserTrace}
              disabled={!hasPrevUserTrace || isLoading}
              className="h-7 w-7 p-0"
            >
              <ChevronLeftIcon className="h-4 w-4" />
            </Button>
          </TooltipTrigger>
          <TooltipContent>
            <p>Previous trace for this user (Shift+←)</p>
          </TooltipContent>
        </Tooltip>

        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              variant="ghost"
              size="sm"
              onClick={onNextUserTrace}
              disabled={!hasNextUserTrace || isLoading}
              className="h-7 w-7 p-0"
            >
              <ChevronRightIcon className="h-4 w-4" />
            </Button>
          </TooltipTrigger>
          <TooltipContent>
            <p>Next trace for this user (Shift+→)</p>
          </TooltipContent>
        </Tooltip>
      </div>
    </div>
  );
}
