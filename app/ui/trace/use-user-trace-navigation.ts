import { useMemo, useCallback, useEffect, useState } from "react";
import { useBtql } from "#/utils/btql/btql";
import { useBtqlQueryBuilder } from "#/utils/btql/use-query-builder";
import { type RowId } from "#/utils/diffs/diff-objects";
import { useActiveRowAndSpan } from "#/ui/query-parameters";
import { useDebouncedCallback } from "#/utils/useDebouncedCallback";
import { useTransitionWithDelay } from "#/utils/use-transition-with-delay";

interface UserTraceNavigationOptions {
  objectType: string;
  objectId: string | null;
  currentRowId: RowId | null;
  enabled?: boolean;
}

interface UserTraceNavigationResult {
  userTraces: RowId[];
  currentUserTraceIndex: number;
  hasNextUserTrace: boolean;
  hasPrevUserTrace: boolean;
  onNextUserTrace: () => void;
  onPrevUserTrace: () => void;
  isLoading: boolean;
  currentUser: string | null;
}

/**
 * Hook to navigate between traces for the same user
 */
export function useUserTraceNavigation({
  objectType,
  objectId,
  currentRowId,
  enabled = true,
}: UserTraceNavigationOptions): UserTraceNavigationResult {
  const builder = useBtqlQueryBuilder({});
  const [, setActiveRowAndSpan] = useActiveRowAndSpan();
  const [currentUser, setCurrentUser] = useState<string | null>(null);
  const [userTraces, setUserTraces] = useState<RowId[]>([]);

  const {
    isDelayedPending: isDelayedSpanChangeTransitioning,
    startTransition: startSpanChangeTransition,
  } = useTransitionWithDelay(100);

  // Query to get the current trace's user information
  const currentTraceQuery = useMemo(() => {
    if (!objectId || !currentRowId || !enabled) return null;
    
    return {
      from: builder.from(objectType, [objectId]),
      select: [
        { alias: "id", expr: builder.ident("id") },
        { alias: "metadata", expr: builder.ident("metadata") },
        { alias: "root_span_id", expr: builder.ident("root_span_id") },
      ],
      filter: builder.and({
        op: "eq" as const,
        left: builder.ident("id"),
        right: { op: "literal" as const, value: currentRowId },
      }),
      limit: 1,
    };
  }, [objectId, currentRowId, enabled, objectType, builder]);

  const { data: currentTraceData, isLoading: isCurrentTraceLoading } = useBtql({
    name: "Current trace user info",
    query: currentTraceQuery,
    disableLimit: true,
  });

  // Extract user from current trace metadata
  useEffect(() => {
    if (!currentTraceData) return;
    
    const rows = currentTraceData.toArray();
    if (rows.length === 0) return;
    
    const row = rows[0];
    const metadata = row.metadata;
    
    let user = null;
    if (metadata) {
      try {
        const parsedMetadata = typeof metadata === 'string' ? JSON.parse(metadata) : metadata;
        // Look for user information in various common fields
        user = parsedMetadata.user_id || 
               parsedMetadata.userId || 
               parsedMetadata.user?.id || 
               parsedMetadata.user?.user_id ||
               parsedMetadata.user;
      } catch (e) {
        console.warn("Failed to parse metadata for user extraction:", e);
      }
    }
    
    setCurrentUser(user);
  }, [currentTraceData]);

  // Query to get all traces for the current user
  const userTracesQuery = useMemo(() => {
    if (!objectId || !currentUser || !enabled) return null;
    
    return {
      from: builder.from(objectType, [objectId]),
      select: [
        { alias: "id", expr: builder.ident("id") },
        { alias: "created", expr: builder.ident("created") },
        { alias: "root_span_id", expr: builder.ident("root_span_id") },
      ],
      filter: builder.or(
        // Try different metadata field patterns for user identification
        {
          op: "eq" as const,
          left: builder.ident("metadata", "user_id"),
          right: { op: "literal" as const, value: currentUser },
        },
        {
          op: "eq" as const,
          left: builder.ident("metadata", "userId"),
          right: { op: "literal" as const, value: currentUser },
        },
        {
          op: "eq" as const,
          left: builder.ident("metadata", "user", "id"),
          right: { op: "literal" as const, value: currentUser },
        },
        {
          op: "eq" as const,
          left: builder.ident("metadata", "user", "user_id"),
          right: { op: "literal" as const, value: currentUser },
        },
        {
          op: "eq" as const,
          left: builder.ident("metadata", "user"),
          right: { op: "literal" as const, value: currentUser },
        }
      ),
      sort: [
        { expr: builder.ident("created"), order: "desc" as const }
      ],
      limit: 100, // Reasonable limit for navigation
    };
  }, [objectId, currentUser, enabled, objectType, builder]);

  const { data: userTracesData, isLoading: isUserTracesLoading } = useBtql({
    name: "User traces",
    query: userTracesQuery,
    disableLimit: true,
  });

  // Process user traces data
  useEffect(() => {
    if (!userTracesData) return;
    
    const rows = userTracesData.toArray();
    const traces = rows.map(row => row.id as RowId);
    setUserTraces(traces);
  }, [userTracesData]);

  // Find current trace index in user traces
  const currentUserTraceIndex = useMemo(() => {
    if (!currentRowId || userTraces.length === 0) return -1;
    return userTraces.findIndex(traceId => traceId === currentRowId);
  }, [currentRowId, userTraces]);

  const hasNextUserTrace = currentUserTraceIndex >= 0 && currentUserTraceIndex < userTraces.length - 1;
  const hasPrevUserTrace = currentUserTraceIndex > 0;

  const onNextUserTrace = useDebouncedCallback(() => {
    if (!hasNextUserTrace) return;
    
    const nextTraceId = userTraces[currentUserTraceIndex + 1];
    if (nextTraceId) {
      startSpanChangeTransition(() => {
        setActiveRowAndSpan({ r: nextTraceId, s: null });
      });
    }
  }, 10);

  const onPrevUserTrace = useDebouncedCallback(() => {
    if (!hasPrevUserTrace) return;
    
    const prevTraceId = userTraces[currentUserTraceIndex - 1];
    if (prevTraceId) {
      startSpanChangeTransition(() => {
        setActiveRowAndSpan({ r: prevTraceId, s: null });
      });
    }
  }, 10);

  return {
    userTraces,
    currentUserTraceIndex,
    hasNextUserTrace,
    hasPrevUserTrace,
    onNextUserTrace,
    onPrevUserTrace,
    isLoading: isCurrentTraceLoading || isUserTracesLoading,
    currentUser,
  };
}
