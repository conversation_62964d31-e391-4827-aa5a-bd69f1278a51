import { type OrgUsersContextT } from "#/utils/org-users-context";
import { type FormatterProps } from "#/ui/arrow-table";
import { Avatar, AvatarGroup, type AvatarSize } from "#/ui/avatar";
import { Vector } from "apache-arrow";
import React from "react";
import { NullFormatter } from "./null-formatter";
import { cn } from "#/utils/classnames";
import { parse } from "#/utils/string";
import { BT_IS_GROUP } from "#/ui/table/grouping/queries";
import { auditLogBaseEventSchema } from "@braintrust/local/api-schema";
import { z } from "zod";
import { getActorNameForActivity } from "#/ui/trace/activity";
import { parseMentions } from "#/utils/mentions";
import { CommentErrorBoundary } from "#/ui/comment-error-boundary";

export const CommentsFormatterFactory = (
  orgUsers: OrgUsersContextT["orgUsers"],
) =>
  function CommentsFormatterFn<
    TsData extends { [BT_IS_GROUP]?: boolean },
    TsValue,
  >(props: FormatterProps<TsData, TsValue>) {
    return (
      <CommentErrorBoundary className="p-1">
        <CommentsFormatter {...props} orgUsers={orgUsers} />
      </CommentErrorBoundary>
    );
  };

function CommentsFormatter<TsData extends { [BT_IS_GROUP]?: boolean }, TsValue>(
  props: FormatterProps<TsData, TsValue> & {
    orgUsers: OrgUsersContextT["orgUsers"];
  },
) {
  const { value: valueProp, renderForTooltip, orgUsers } = props;

  const valueArray =
    valueProp instanceof Vector ? valueProp.toArray() : parse(valueProp);
  if (!Array.isArray(valueArray)) {
    if (props.cell.row.original[BT_IS_GROUP]) {
      return null;
    }
    return <NullFormatter />;
  }
  const values = valueArray.map((v) => ("toJSON" in v ? v.toJSON() : v));

  const parsedData: {
    actorName: string;
    userId: string;
    imgUrl: string | undefined | null;
    text: string;
    size: AvatarSize;
  }[] = values.flatMap((v) => {
    let metadata;
    let comment;
    try {
      metadata =
        typeof v.metadata === "string" ? JSON.parse(v.metadata) : v.metadata;
      comment =
        typeof v.comment === "string" ? JSON.parse(v.comment) : v.comment;
    } catch {
      return [];
    }

    const parsedUserId = z.string().nullish().safeParse(metadata.user_id);
    const userId =
      parsedUserId.success && parsedUserId.data ? parsedUserId.data : "";
    const user = orgUsers[userId];

    const parsedCommentText = z.string().nullish().safeParse(comment.text);
    const text =
      parsedCommentText.success && parsedCommentText.data
        ? parsedCommentText.data
        : "";
    const commentText = parseMentions({
      text,
      orgUsers,
    });

    const parsedSource = auditLogBaseEventSchema.shape.source.safeParse(
      v.source,
    );
    const actorName = parsedSource.success
      ? getActorNameForActivity(userId, parsedSource.data, orgUsers)
      : `${user?.given_name ?? ""} ${user?.family_name ?? "Unknown"}`;
    return [
      {
        actorName,
        userId,
        imgUrl: user?.avatar_url,
        text: commentText,
        size: "xs",
      },
    ];
  });

  if (renderForTooltip) {
    return (
      <div className="flex flex-col gap-y-2 text-primary-800">
        {parsedData.map((d, i) => (
          <div
            key={i}
            className={cn("flex flex-col gap-1.5", {
              "border-t pt-2": i > 0,
            })}
          >
            <div className="flex items-center gap-x-1">
              <Avatar className="flex-none" {...d} />
              <span className="text-primary-500">
                <span className="font-medium text-primary-700">
                  {d.actorName}
                </span>{" "}
                commented
              </span>
            </div>
            <div className="hyphens-auto whitespace-pre-wrap">{d.text}</div>
          </div>
        ))}
      </div>
    );
  }

  if (parsedData.length === 1) {
    return (
      <div className="flex items-center gap-x-1">
        <Avatar className="flex-none" {...parsedData[0]} />
        <span className="flex-1 truncate">{parsedData[0].text}</span>
      </div>
    );
  }

  const userIdsSet = new Set<string>();
  const dedupedUsers = parsedData.filter((d) => {
    if (userIdsSet.has(d.userId)) {
      return false;
    }
    userIdsSet.add(d.userId);
    return true;
  });

  return (
    <div className="flex items-center gap-x-1">
      <AvatarGroup className="flex-none" avatars={dedupedUsers} />
      <span className="truncate">{parsedData.length}</span>
    </div>
  );
}
