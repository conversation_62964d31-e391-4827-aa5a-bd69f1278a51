import { But<PERSON> } from "#/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "#/ui/dropdown-menu";
import { type ReactNode } from "react";
import { type FilterEditorColumnType } from "#/ui/table/filter-editor";

export function FilterEditorOperatorSelect({
  selectedOperator,
  setOperator,
  operators,
  type,
  hasComparisonExperiments,
}: {
  selectedOperator?: FilterEditorOperator;
  setOperator: (operatorId: string) => void;
  operators: FilterEditorOperator[];
  type: FilterEditorColumnType;
  hasComparisonExperiments?: boolean;
}) {
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button size="xs" isDropdown>
          {selectedOperator?.label ?? "Select operator"}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="start">
        <DropdownMenuGroup>
          {operators.map((op) => {
            const isHidden = !op.type.some((t) => t === type);
            if (isHidden) return null;
            if (op.isSeparator) {
              return (
                <DropdownMenuSeparator
                  key={Array.isArray(op.value) ? op.value[0] : op.value}
                />
              );
            }
            return (
              <DropdownMenuItem
                onSelect={() => {
                  setOperator(op.id);
                }}
                key={op.id}
              >
                {op.label}
              </DropdownMenuItem>
            );
          })}
        </DropdownMenuGroup>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

type FilterValue = string | { joinExpr: string; values: string[] };

export type FilterEditorOperator = {
  id: string;
  label: ReactNode;
  value: FilterValue;
  prefixValue?: string;
  type: string[];
  hideInput?: boolean;
  isSeparator?: boolean;
  wrapLikeValue?: boolean;
  isRegressionFilter?: boolean;
};

export const defaultOperators: FilterEditorOperator[] = [
  {
    id: "contains",
    label: "contains",
    value: "ILIKE",
    wrapLikeValue: true,
    type: ["comments"],
  },
  {
    id: "does-not-contain",
    label: "does not contain",
    value: "ILIKE",
    prefixValue: "not",
    wrapLikeValue: true,
    type: [],
  },
  {
    id: "matches",
    label: "matches",
    value: "MATCH",
    type: ["text", "full-text", "comments"],
  },
  {
    id: "does-not-match",
    label: "does not match",
    value: "MATCH",
    prefixValue: "not",
    type: ["text", "full-text"],
  },
  {
    id: "tags-includes",
    label: "includes",
    value: "includes",
    type: ["tags"],
  },
  {
    id: "tags-not-includes",
    label: "does not include",
    value: "not includes",
    type: ["tags"],
  },
  {
    id: "equals",
    label: "is",
    value: "=",
    type: [
      "text",
      "full-text",
      "score",
      "number",
      "id",
      "span_name",
      "comments",
    ],
  },
  {
    id: "does-not-equal",
    label: "is not",
    value: "!=",
    type: ["text", "full-text", "score", "number", "id", "span_name"],
  },
  {
    id: "greater-than",
    label: "greater than",
    value: ">",
    type: ["score", "number"],
  },
  {
    id: "less-than",
    label: "less than",
    value: "<",
    type: ["score", "number"],
  },
  {
    id: "greater-than-or-equal-to",
    label: "greater than or equal to",
    value: ">=",
    type: ["score", "number"],
  },
  {
    id: "less-than-or-equal-to",
    label: "less than or equal to",
    value: "<=",
    type: ["score", "number"],
  },
  {
    id: "is-empty",
    label: "is empty",
    value: "IS NULL",
    type: ["score", "number", "tags"],
    hideInput: true,
  },
  {
    id: "is-empty-text",
    label: "is empty",
    value: { joinExpr: "OR", values: ["IS NULL", '= ""'] },
    type: ["text", "full-text"],
    hideInput: true,
  },
  {
    id: "is-empty-number",
    label: "is empty",
    value: { joinExpr: "OR", values: ["IS NULL", "= 0"] },
    type: ["number"],
    hideInput: true,
  },
  {
    id: "is-not-empty",
    label: "is not empty",
    value: "IS NOT NULL",
    type: ["score", "tags", "comments"],
    hideInput: true,
  },
  {
    id: "is-not-empty-text",
    label: "is not empty",
    value: { joinExpr: "AND", values: ["IS NOT NULL", '!= ""'] },
    type: ["text", "full-text"],
    hideInput: true,
  },
  {
    id: "is-not-empty-number",
    label: "is not empty",
    value: { joinExpr: "AND", values: ["IS NOT NULL", "!= 0"] },
    type: ["number"],
    hideInput: true,
  },
  {
    id: "in-last-day",
    label: "in last day",
    value: ">= NOW() - INTERVAL 1 DAY",
    type: ["date"],
    hideInput: true,
  },
  {
    id: "in-last-3-days",
    label: "in last 3 days",
    value: ">= NOW() - INTERVAL 3 DAY",
    type: ["date"],
    hideInput: true,
  },
  {
    id: "in-last-7-days",
    label: "in last 7 days",
    value: ">= NOW() - INTERVAL 7 DAY",
    type: ["date"],
    hideInput: true,
  },
  {
    id: "in-last-month",
    label: "in last month",
    value: ">= NOW() - INTERVAL 1 MONTH",
    type: ["date"],
    hideInput: true,
  },
  {
    id: "in-last-year",
    label: "in last year",
    value: ">= NOW() - INTERVAL 1 YEAR",
    type: ["date"],
    hideInput: true,
  },
  {
    id: "custom-date-range",
    label: "between",
    value: ">=",
    type: ["date"],
  },
  {
    id: "is-true",
    label: "is true",
    value: "= true",
    type: ["boolean"],
    hideInput: true,
  },
  {
    id: "is-false",
    label: "is false",
    value: "= false",
    type: ["boolean"],
    hideInput: true,
  },
];

export const scoreComparisonOperators = [
  {
    id: "separator",
    label: "",
    value: "__SEPARATOR__",
    isSeparator: true,
    type: ["score"],
  },
  {
    id: "regressions",
    label: (
      <span>
        is <span className="text-bad-700">lower</span> than comparison
        experiment
      </span>
    ),
    value: "regressions",
    type: ["score"],
    hideInput: true,
    isRegressionFilter: true,
  },
  {
    id: "improvements",
    label: (
      <span>
        is <span className="text-good-700">higher</span> than comparison
        experiment
      </span>
    ),
    value: "improvements",
    type: ["score"],
    hideInput: true,
    isRegressionFilter: true,
  },
  {
    id: "scores-equal",
    label: "is equal to comparison experiment",
    value: "equal",
    type: ["score"],
    hideInput: true,
    isRegressionFilter: true,
  },
];
